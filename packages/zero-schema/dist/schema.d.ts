export declare const company_brand: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "company_brand";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly company_id: {
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        };
        readonly has_brand_setup: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly updated_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly brand_name: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly is_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly error_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly brand_profile: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly messaging_strategy: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly visual_identity: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly product_catalog: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly prompt_library: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const company_campaigns: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "company_campaigns";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly created_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly company_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly user_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly name: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly slug: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly messaging: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly value_prop: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly objective: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly identity: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly kpis: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly objectives: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly guidelines: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly personas: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly personality: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly targetAudience: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly tone: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly visualStyle: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly voice: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly products: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly metadata: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly status: Omit<{
            type: "string";
            optional: false;
            customType: "Draft" | "Published" | "Archived";
        }, "optional"> & {
            optional: true;
        };
        readonly end_date: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly start_date: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly has_reached_summary: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly external_research: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly is_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly error_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly target_icps: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly target_personas: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly channels: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly color_tag: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly posts_per_week: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const campaign_templates: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "campaign_templates";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly created_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly title: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly goal: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly description: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly style: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly image_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly duration_weeks: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const post_templates: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "post_templates";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly created_at: {
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        };
        readonly title: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly description: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly style: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly image_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly content_type: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly channel: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const saved_research: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "saved_research";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly account_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly icp_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly persona_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly title: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly description: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly source: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly relevance_score: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly research_type: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly time_filter: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly updated_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly source_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly topic: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly archived: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const user_cache: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "user_cache";
    columns: {
        readonly created_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly selected_campaign: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly selected_video_project: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly selected_scene_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly user_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly task_list_columns: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly task_sorting_state: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["user_id"];
}>;
export declare const products: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "products";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly created_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly updated_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly name: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly description: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly target_audience: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly key_features: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly company_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly custom_fields: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const product_documents: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "product_documents";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly created_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly title: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly file_path: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly company_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly content: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly file_type: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly product_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const site_research: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "site_research";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly company_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly created_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly icps: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly personal: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly urls: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly instruction: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly schema: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly enable_web_search: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly agent_mode: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly is_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly error_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly results: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const socials_research: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "socials_research";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly company_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly created_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly keywords: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly platform: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly is_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly error_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly results: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const generated_research: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "generated_research";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly account_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly icp_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly persona_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly research_type: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly time_filter: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly title: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly results: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly content_suggestions: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly updated_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly created_by: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly updated_by: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly is_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly topic: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const company_content: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "company_content";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly campaign_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly company_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly idea_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly content_type: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly language: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly content: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly updated_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly image_path: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly content_template: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly image_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly channel: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly has_image: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly visual_description: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly avatar_script: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly avatar_voice_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly avatar_presenter_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly avatar_video_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly avatar_video_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly is_avatar_ready: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly has_avatar: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly is_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly status: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly task_description: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly task_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly task_title: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly has_video_presentation: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly video_presentation_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly video_presentation_render_params: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly video_presentation_script: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly seo_keywords: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly trend_keywords: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly is_posted: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly is_scheduled: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly is_draft: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly visual_description_group: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly content_editor_template: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly scheduled_publishing_time: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly video_editor_overlays: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly video_editor_aspect_ratio: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly video_editor_player_dimensions: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly image_urls: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly archived: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly kanban_order: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly is_published: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly scheduled_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly published_by: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly published_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly assigned_to: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly schedule_date: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly ayrshare_post_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly is_paused: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly ayrshare_post_status: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly error_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const ayrshare_user_profile: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "ayrshare_user_profile";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly user_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly created_at: {
            type: "number";
            optional: false;
            customType: number;
        };
        readonly title: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly refId: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly profileKey: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly messagingActive: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly company_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly description: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly is_active: {
            type: "boolean";
            optional: false;
            customType: boolean;
        };
        readonly profile_name: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly is_shared: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly permissions: Omit<{
            type: "json";
            optional: false;
            customType: {
                can_post: boolean;
                can_view: boolean;
                can_analytics: boolean;
            };
        }, "optional"> & {
            optional: true;
        };
        readonly updated_at: {
            type: "number";
            optional: false;
            customType: number;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const post_engagement_details: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "post_engagement_details";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly platform_post_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly engagement_info: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly is_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly error_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly profile_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly engaged_users: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly company_id: {
            type: "string";
            optional: false;
            customType: string;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const ayrshare_social_profiles: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "ayrshare_social_profiles";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly post_history: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly refId: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly is_shared: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly user_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly company_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly platform: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly display_name: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly username: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly user_image: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly profile_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly headline: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly subscription_type: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly verified_type: Omit<{
            type: "string";
            optional: false;
            customType: string;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly refresh_days_remaining: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly refresh_required: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly is_connected: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly connected_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly updated_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const company_task_statuses: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "company_task_statuses";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly company_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly name: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly display_name: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly color: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly status_order: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly icon: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const image_conversations: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "image_conversations";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly image_path: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly user_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly company_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly updated_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const image_messages: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "image_messages";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly content: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly role: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly user_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly company_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly conversation_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly image_path: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly reference_images: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const video_conversations: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "video_conversations";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly title: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly user_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly company_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly video_project_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly updated_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const video_messages: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "video_messages";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly content: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly role: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly user_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly company_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly conversation_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly video_project_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        } & {
            serverName: string;
        }, "optional"> & {
            optional: true;
        };
        readonly is_generating: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly is_error: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly video_generation_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly video_generation_model: Omit<{
            type: "string";
            optional: false;
            customType: "veo-3-fast" | "veo-3-standard";
        }, "optional"> & {
            optional: true;
        };
        readonly video_generation_status: Omit<{
            type: "string";
            optional: false;
            customType: "pending" | "processing" | "completed" | "failed" | "cancelled";
        }, "optional"> & {
            optional: true;
        };
        readonly video_generation_prompt: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly generated_video_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly generation_error_message: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly generation_started_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly generation_completed_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const video_generation_jobs: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "video_generation_jobs";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly message_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly account_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly user_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly model: {
            type: "string";
            optional: false;
            customType: "veo-3-fast" | "veo-3-standard";
        };
        readonly prompt: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly improved_prompt: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly external_operation_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly external_operation_name: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly status: {
            type: "string";
            optional: false;
            customType: "pending" | "processing" | "completed" | "failed" | "cancelled";
        };
        readonly error_message: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly generated_video_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly video_storage_path: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly generation_config: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly updated_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly started_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly completed_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly overlay_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly project_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly scene_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const video_projects: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "video_projects";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly account_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly user_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly name: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly description: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly aspect_ratio: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly duration_frames: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly fps: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly width: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly height: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly thumbnail_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly status: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly is_template: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly template_category: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly template_tags: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly updated_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly created_by: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly updated_by: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const video_project_scenes: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "video_project_scenes";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly project_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly user_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly name: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly description: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly order_index: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly duration_frames: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly aspect_ratio: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly updated_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const video_project_scene_selections: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "video_project_scene_selections";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly project_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly user_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly selected_scene_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly updated_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const video_project_overlays: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "video_project_overlays";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly scene_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly project_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly type: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly durationInFrames: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly from: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly height: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly row: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly left: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly top: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly width: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly isDragging: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly rotation: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly loading: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly content: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly src: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly styles: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly transcript: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly videoStartTime: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly startFromSound: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly speed: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly duration: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly captions: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly template: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly category: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly updated_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly metadata: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly video_prompt: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const video_project_assets: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "video_project_assets";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly project_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly account_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly asset_type: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly original_filename: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly file_path: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly file_size: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly mime_type: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly duration_seconds: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly thumbnail_path: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly metadata: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly is_external: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly external_source: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly external_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const video_project_autosaves: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "video_project_autosaves";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly scene_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly project_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly user_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly editor_state: {
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        };
        readonly save_type: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const video_renders: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "video_renders";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly project_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly account_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly user_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly render_id: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly status: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly progress: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly output_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly output_size_bytes: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly error_message: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly render_settings: Omit<{
            type: "json";
            optional: false;
            customType: import("@rocicorp/zero").ReadonlyJSONValue;
        }, "optional"> & {
            optional: true;
        };
        readonly started_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly completed_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly created_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const assets: import("@rocicorp/zero").TableBuilderWithColumns<{
    name: "assets";
    columns: {
        readonly id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly account_id: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly file_name: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly file_path: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly file_size: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly file_type: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly folder_name: {
            type: "string";
            optional: false;
            customType: string;
        };
        readonly original_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly file_extension: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly transcript_path: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly transcript_url: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly has_transcript: Omit<{
            type: "boolean";
            optional: false;
            customType: boolean;
        }, "optional"> & {
            optional: true;
        };
        readonly transcript_extracted_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly processing_status: Omit<{
            type: "string";
            optional: false;
            customType: "processing" | "completed" | "failed";
        }, "optional"> & {
            optional: true;
        };
        readonly processing_started_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly processing_completed_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly processing_error: Omit<{
            type: "string";
            optional: false;
            customType: string;
        }, "optional"> & {
            optional: true;
        };
        readonly uploaded_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
        readonly updated_at: Omit<{
            type: "number";
            optional: false;
            customType: number;
        }, "optional"> & {
            optional: true;
        };
    };
    primaryKey: readonly [string, ...string[]];
} & {
    primaryKey: ["id"];
}>;
export declare const schema: {
    tables: {
        readonly company_brand: {
            name: "company_brand";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly company_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                };
                readonly has_brand_setup: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly brand_name: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly error_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly brand_profile: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly messaging_strategy: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly visual_identity: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly product_catalog: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly prompt_library: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly company_campaigns: {
            name: "company_campaigns";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly created_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly company_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly user_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly name: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly slug: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly messaging: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly value_prop: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly objective: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly identity: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly kpis: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly objectives: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly guidelines: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly personas: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly personality: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly targetAudience: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly tone: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly visualStyle: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly voice: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly products: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly metadata: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly status: Omit<{
                    type: "string";
                    optional: false;
                    customType: "Draft" | "Published" | "Archived";
                }, "optional"> & {
                    optional: true;
                };
                readonly end_date: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly start_date: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly has_reached_summary: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly external_research: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly error_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly target_icps: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly target_personas: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly channels: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly color_tag: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly posts_per_week: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly personas: {
            name: "personas";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly name: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly company_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly icp_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly data: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly error_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly products: {
            name: "products";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly created_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly updated_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly name: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly description: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly target_audience: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly key_features: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly company_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly custom_fields: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly campaign_templates: {
            name: "campaign_templates";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly created_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly title: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly goal: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly description: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly style: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly image_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly duration_weeks: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly post_templates: {
            name: "post_templates";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly created_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                };
                readonly title: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly description: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly style: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly image_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly content_type: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly channel: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly saved_research: {
            name: "saved_research";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly account_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly icp_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly persona_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly title: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly description: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly source: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly relevance_score: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly research_type: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly time_filter: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly source_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly topic: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly archived: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly user_cache: {
            name: "user_cache";
            columns: {
                readonly created_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly selected_campaign: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly selected_video_project: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly selected_scene_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly task_list_columns: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly task_sorting_state: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["user_id"];
        };
        readonly product_documents: {
            name: "product_documents";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly created_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly title: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly file_path: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly company_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly content: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly file_type: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly product_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly site_research: {
            name: "site_research";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly company_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly created_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly icps: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly personal: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly urls: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly instruction: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly schema: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly enable_web_search: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly agent_mode: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly error_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly results: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly icps: {
            name: "icps";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly name: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly company_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly data: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly error_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly reference_products: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly reference_description: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly linkedInUrls: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly withLinkedIn: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly withAi: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly socials_research: {
            name: "socials_research";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly company_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly created_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly keywords: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly platform: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly error_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly results: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly generated_research: {
            name: "generated_research";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly account_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly icp_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly persona_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly research_type: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly time_filter: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly title: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly results: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly content_suggestions: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly updated_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly created_by: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_by: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly topic: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly company_content: {
            name: "company_content";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly campaign_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly company_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly idea_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly content_type: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly language: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly content: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly updated_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly image_path: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly content_template: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly image_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly channel: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly has_image: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly visual_description: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly avatar_script: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly avatar_voice_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly avatar_presenter_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly avatar_video_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly avatar_video_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_avatar_ready: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly has_avatar: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly status: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly task_description: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly task_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly task_title: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly has_video_presentation: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly video_presentation_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly video_presentation_render_params: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly video_presentation_script: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly seo_keywords: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly trend_keywords: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_posted: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_scheduled: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_draft: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly visual_description_group: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly content_editor_template: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly scheduled_publishing_time: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly video_editor_overlays: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly video_editor_aspect_ratio: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly video_editor_player_dimensions: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly image_urls: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly archived: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly kanban_order: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_published: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly scheduled_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly published_by: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly published_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly assigned_to: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly schedule_date: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly ayrshare_post_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_paused: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly ayrshare_post_status: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly error_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly ayrshare_user_profile: {
            name: "ayrshare_user_profile";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly created_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly title: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly refId: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly profileKey: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly messagingActive: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly company_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly description: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_active: {
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                };
                readonly profile_name: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_shared: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly permissions: Omit<{
                    type: "json";
                    optional: false;
                    customType: {
                        can_post: boolean;
                        can_view: boolean;
                        can_analytics: boolean;
                    };
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly post_engagement_details: {
            name: "post_engagement_details";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly platform_post_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly engagement_info: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly error_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly profile_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly engaged_users: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly company_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly ayrshare_social_profiles: {
            name: "ayrshare_social_profiles";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly post_history: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly refId: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_shared: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly user_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly company_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly platform: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly display_name: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly username: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly user_image: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly profile_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly headline: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly subscription_type: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly verified_type: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly refresh_days_remaining: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly refresh_required: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_connected: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly connected_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly company_task_statuses: {
            name: "company_task_statuses";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly company_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly name: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly display_name: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly color: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly status_order: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly icon: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly image_conversations: {
            name: "image_conversations";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly image_path: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly company_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly image_messages: {
            name: "image_messages";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly content: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly role: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly company_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly conversation_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly image_path: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly reference_images: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly video_conversations: {
            name: "video_conversations";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly title: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly company_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly video_project_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly video_messages: {
            name: "video_messages";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly content: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly role: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly company_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly conversation_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly video_project_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                } & {
                    serverName: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_generating: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_error: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly video_generation_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly video_generation_model: Omit<{
                    type: "string";
                    optional: false;
                    customType: "veo-3-fast" | "veo-3-standard";
                }, "optional"> & {
                    optional: true;
                };
                readonly video_generation_status: Omit<{
                    type: "string";
                    optional: false;
                    customType: "pending" | "processing" | "completed" | "failed" | "cancelled";
                }, "optional"> & {
                    optional: true;
                };
                readonly video_generation_prompt: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly generated_video_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly generation_error_message: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly generation_started_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly generation_completed_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly video_generation_jobs: {
            name: "video_generation_jobs";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly message_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly account_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly model: {
                    type: "string";
                    optional: false;
                    customType: "veo-3-fast" | "veo-3-standard";
                };
                readonly prompt: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly improved_prompt: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly external_operation_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly external_operation_name: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly status: {
                    type: "string";
                    optional: false;
                    customType: "pending" | "processing" | "completed" | "failed" | "cancelled";
                };
                readonly error_message: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly generated_video_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly video_storage_path: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly generation_config: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly started_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly completed_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly overlay_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly project_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly scene_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly video_projects: {
            name: "video_projects";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly account_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly name: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly description: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly aspect_ratio: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly duration_frames: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly fps: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly width: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly height: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly thumbnail_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly status: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_template: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly template_category: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly template_tags: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_by: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_by: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly video_project_scenes: {
            name: "video_project_scenes";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly project_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly name: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly description: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly order_index: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly duration_frames: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly aspect_ratio: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly video_project_scene_selections: {
            name: "video_project_scene_selections";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly project_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly selected_scene_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly video_project_overlays: {
            name: "video_project_overlays";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly scene_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly project_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly type: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly durationInFrames: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly from: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly height: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly row: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly left: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly top: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly width: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly isDragging: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly rotation: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly loading: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly content: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly src: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly styles: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly transcript: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly videoStartTime: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly startFromSound: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly speed: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly duration: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly captions: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly template: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly category: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly metadata: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly video_prompt: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly video_project_assets: {
            name: "video_project_assets";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly project_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly account_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly asset_type: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly original_filename: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly file_path: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly file_size: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly mime_type: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly duration_seconds: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly thumbnail_path: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly metadata: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_external: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly external_source: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly external_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly video_project_autosaves: {
            name: "video_project_autosaves";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly scene_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly project_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly editor_state: {
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                };
                readonly save_type: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly video_renders: {
            name: "video_renders";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly project_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly account_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly render_id: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly status: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly progress: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly output_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly output_size_bytes: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly error_message: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly render_settings: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
                readonly started_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly completed_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly assets: {
            name: "assets";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly account_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly file_name: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly file_path: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly file_size: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly file_type: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly folder_name: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly original_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly file_extension: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly transcript_path: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly transcript_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly has_transcript: Omit<{
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                }, "optional"> & {
                    optional: true;
                };
                readonly transcript_extracted_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly processing_status: Omit<{
                    type: "string";
                    optional: false;
                    customType: "processing" | "completed" | "failed";
                }, "optional"> & {
                    optional: true;
                };
                readonly processing_started_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly processing_completed_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly processing_error: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly uploaded_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly accounts_memberships: {
            name: "accounts_memberships";
            columns: {
                readonly user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly account_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly account_role: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["user_id", "account_id"];
        };
        readonly subscriptions: {
            name: "subscriptions";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly account_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly billing_customer_id: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly active: {
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                };
                readonly status: {
                    type: "string";
                    optional: false;
                    customType: "active" | "trialing" | "canceled" | "past_due" | "unpaid" | "incomplete" | "incomplete_expired" | "paused";
                };
                readonly billing_provider: {
                    type: "string";
                    optional: false;
                    customType: "stripe" | "lemonsqueezy";
                };
                readonly cancel_at_period_end: {
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                };
                readonly currency: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly period_starts_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly period_ends_at: {
                    type: "number";
                    optional: false;
                    customType: number;
                };
                readonly trial_starts_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly trial_ends_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
        readonly accounts: {
            name: "accounts";
            columns: {
                readonly id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly primary_owner_user_id: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly name: {
                    type: "string";
                    optional: false;
                    customType: string;
                };
                readonly website: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly slug: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly email: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly is_personal_account: {
                    type: "boolean";
                    optional: false;
                    customType: boolean;
                };
                readonly updated_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_at: Omit<{
                    type: "number";
                    optional: false;
                    customType: number;
                }, "optional"> & {
                    optional: true;
                };
                readonly created_by: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly updated_by: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly picture_url: Omit<{
                    type: "string";
                    optional: false;
                    customType: string;
                }, "optional"> & {
                    optional: true;
                };
                readonly public_data: Omit<{
                    type: "json";
                    optional: false;
                    customType: import("@rocicorp/zero").ReadonlyJSONValue;
                }, "optional"> & {
                    optional: true;
                };
            };
            primaryKey: readonly [string, ...string[]];
        } & {
            primaryKey: ["id"];
        };
    };
    relationships: {
        readonly company_brand: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
        };
        readonly company_campaigns: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
        };
        readonly personas: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
        };
        readonly products: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            product_documents: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "company_id" | "title" | "file_path" | "file_type" | "content" | "product_id")[];
                readonly destSchema: "product_documents";
                readonly cardinality: "many";
            }];
        };
        readonly saved_research: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
        };
        readonly product_documents: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            product: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "company_id" | "updated_at" | "name" | "description" | "target_audience" | "key_features" | "custom_fields")[];
                readonly destSchema: "products";
                readonly cardinality: "one";
            }];
        };
        readonly site_research: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
        };
        readonly icps: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
        };
        readonly socials_research: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
        };
        readonly generated_research: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
        };
        readonly company_content: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
        };
        readonly ayrshare_user_profile: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            socialProfiles: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "company_id" | "updated_at" | "user_id" | "platform" | "refId" | "is_shared" | "post_history" | "display_name" | "username" | "user_image" | "profile_url" | "headline" | "subscription_type" | "verified_type" | "refresh_days_remaining" | "refresh_required" | "is_connected" | "connected_at")[];
                readonly destSchema: "ayrshare_social_profiles";
                readonly cardinality: "many";
            }];
        };
        readonly post_engagement_details: {
            ayrshare_user_profile: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "company_id" | "updated_at" | "user_id" | "title" | "description" | "refId" | "profileKey" | "is_active" | "messagingActive" | "profile_name" | "is_shared" | "permissions")[];
                readonly destSchema: "ayrshare_user_profile";
                readonly cardinality: "many";
            }];
        };
        readonly ayrshare_social_profiles: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            userProfile: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "company_id" | "updated_at" | "user_id" | "title" | "description" | "refId" | "profileKey" | "is_active" | "messagingActive" | "profile_name" | "is_shared" | "permissions")[];
                readonly destSchema: "ayrshare_user_profile";
                readonly cardinality: "one";
            }];
        };
        readonly company_task_statuses: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
        };
        readonly image_conversations: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            messages: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "company_id" | "user_id" | "content" | "image_path" | "role" | "conversation_id" | "reference_images")[];
                readonly destSchema: "image_messages";
                readonly cardinality: "many";
            }];
        };
        readonly image_messages: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            conversation: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "company_id" | "updated_at" | "user_id" | "image_path")[];
                readonly destSchema: "image_conversations";
                readonly cardinality: "one";
            }];
        };
        readonly video_conversations: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            messages: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "company_id" | "is_generating" | "user_id" | "content" | "role" | "conversation_id" | "video_project_id" | "is_error" | "video_generation_id" | "video_generation_model" | "video_generation_status" | "video_generation_prompt" | "generated_video_url" | "generation_error_message" | "generation_started_at" | "generation_completed_at")[];
                readonly destSchema: "video_messages";
                readonly cardinality: "many";
            }];
        };
        readonly video_messages: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            conversation: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "company_id" | "updated_at" | "user_id" | "title" | "video_project_id")[];
                readonly destSchema: "video_conversations";
                readonly cardinality: "one";
            }];
            generation_job: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "updated_at" | "user_id" | "status" | "account_id" | "generated_video_url" | "prompt" | "message_id" | "model" | "improved_prompt" | "external_operation_id" | "external_operation_name" | "error_message" | "video_storage_path" | "generation_config" | "started_at" | "completed_at" | "overlay_id" | "project_id" | "scene_id")[];
                readonly destSchema: "video_generation_jobs";
                readonly cardinality: "one";
            }];
        };
        readonly video_generation_jobs: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            message: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "company_id" | "is_generating" | "user_id" | "content" | "role" | "conversation_id" | "video_project_id" | "is_error" | "video_generation_id" | "video_generation_model" | "video_generation_status" | "video_generation_prompt" | "generated_video_url" | "generation_error_message" | "generation_started_at" | "generation_completed_at")[];
                readonly destSchema: "video_messages";
                readonly cardinality: "one";
            }];
        };
        readonly video_projects: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            scenes: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "updated_at" | "name" | "user_id" | "description" | "project_id" | "aspect_ratio" | "duration_frames" | "order_index")[];
                readonly destSchema: "video_project_scenes";
                readonly cardinality: "many";
            }];
            assets: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "metadata" | "account_id" | "file_path" | "project_id" | "asset_type" | "original_filename" | "file_size" | "mime_type" | "duration_seconds" | "thumbnail_path" | "is_external" | "external_source" | "external_id")[];
                readonly destSchema: "video_project_assets";
                readonly cardinality: "many";
            }];
            renders: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "user_id" | "status" | "account_id" | "error_message" | "started_at" | "completed_at" | "project_id" | "render_id" | "progress" | "output_url" | "output_size_bytes" | "render_settings")[];
                readonly destSchema: "video_renders";
                readonly cardinality: "many";
            }];
            scene_selections: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "updated_at" | "user_id" | "selected_scene_id" | "project_id")[];
                readonly destSchema: "video_project_scene_selections";
                readonly cardinality: "many";
            }];
        };
        readonly video_project_scenes: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            project: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "updated_at" | "name" | "user_id" | "status" | "description" | "account_id" | "created_by" | "updated_by" | "aspect_ratio" | "duration_frames" | "fps" | "width" | "height" | "thumbnail_url" | "is_template" | "template_category" | "template_tags")[];
                readonly destSchema: "video_projects";
                readonly cardinality: "one";
            }];
            overlays: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "type" | "updated_at" | "left" | "metadata" | "content" | "project_id" | "scene_id" | "width" | "height" | "durationInFrames" | "from" | "row" | "top" | "isDragging" | "rotation" | "loading" | "src" | "styles" | "transcript" | "videoStartTime" | "startFromSound" | "speed" | "duration" | "captions" | "template" | "category" | "video_prompt")[];
                readonly destSchema: "video_project_overlays";
                readonly cardinality: "many";
            }];
            autosaves: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "user_id" | "project_id" | "scene_id" | "editor_state" | "save_type")[];
                readonly destSchema: "video_project_autosaves";
                readonly cardinality: "many";
            }];
        };
        readonly video_project_scene_selections: {
            project: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "updated_at" | "name" | "user_id" | "status" | "description" | "account_id" | "created_by" | "updated_by" | "aspect_ratio" | "duration_frames" | "fps" | "width" | "height" | "thumbnail_url" | "is_template" | "template_category" | "template_tags")[];
                readonly destSchema: "video_projects";
                readonly cardinality: "one";
            }];
            scene: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "updated_at" | "name" | "user_id" | "description" | "project_id" | "aspect_ratio" | "duration_frames" | "order_index")[];
                readonly destSchema: "video_project_scenes";
                readonly cardinality: "one";
            }];
        };
        readonly video_project_overlays: {
            scene: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "updated_at" | "name" | "user_id" | "description" | "project_id" | "aspect_ratio" | "duration_frames" | "order_index")[];
                readonly destSchema: "video_project_scenes";
                readonly cardinality: "one";
            }];
        };
        readonly video_project_assets: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            project: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "updated_at" | "name" | "user_id" | "status" | "description" | "account_id" | "created_by" | "updated_by" | "aspect_ratio" | "duration_frames" | "fps" | "width" | "height" | "thumbnail_url" | "is_template" | "template_category" | "template_tags")[];
                readonly destSchema: "video_projects";
                readonly cardinality: "one";
            }];
        };
        readonly video_project_autosaves: {
            scene: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "updated_at" | "name" | "user_id" | "description" | "project_id" | "aspect_ratio" | "duration_frames" | "order_index")[];
                readonly destSchema: "video_project_scenes";
                readonly cardinality: "one";
            }];
        };
        readonly video_renders: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            project: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "updated_at" | "name" | "user_id" | "status" | "description" | "account_id" | "created_by" | "updated_by" | "aspect_ratio" | "duration_frames" | "fps" | "width" | "height" | "thumbnail_url" | "is_template" | "template_category" | "template_tags")[];
                readonly destSchema: "video_projects";
                readonly cardinality: "one";
            }];
        };
        readonly assets: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
            account: [{
                readonly sourceField: string[];
                readonly destField: ("id" | "created_at" | "updated_at" | "name" | "slug" | "created_by" | "updated_by" | "primary_owner_user_id" | "is_personal_account" | "website" | "email" | "picture_url" | "public_data")[];
                readonly destSchema: "accounts";
                readonly cardinality: "one";
            }];
        };
        readonly accounts_memberships: {
            companyMemberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
        };
        readonly subscriptions: {
            memberships: [{
                readonly sourceField: string[];
                readonly destField: ("created_at" | "updated_at" | "user_id" | "account_id" | "account_role")[];
                readonly destSchema: "accounts_memberships";
                readonly cardinality: "many";
            }];
        };
    };
};
export type Schema = typeof schema;
export declare const permissions: Promise<{
    [x: string]: unknown;
    tables: Record<string, {
        row?: {
            select?: ["allow", import("@rocicorp/zero").Condition][] | undefined;
            insert?: ["allow", import("@rocicorp/zero").Condition][] | undefined;
            update?: {
                preMutation?: ["allow", import("@rocicorp/zero").Condition][] | undefined;
                postMutation?: ["allow", import("@rocicorp/zero").Condition][] | undefined;
            } | undefined;
            delete?: ["allow", import("@rocicorp/zero").Condition][] | undefined;
        } | undefined;
        cell?: Record<string, {
            select?: ["allow", import("@rocicorp/zero").Condition][] | undefined;
            insert?: ["allow", import("@rocicorp/zero").Condition][] | undefined;
            update?: {
                preMutation?: ["allow", import("@rocicorp/zero").Condition][] | undefined;
                postMutation?: ["allow", import("@rocicorp/zero").Condition][] | undefined;
            } | undefined;
            delete?: ["allow", import("@rocicorp/zero").Condition][] | undefined;
        }> | undefined;
    }>;
}>;
