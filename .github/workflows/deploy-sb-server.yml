name: Deploy SB Server

on:
  workflow_call:
    inputs:
      environment:
        description: 'Target environment (preview | staging | production)'
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    timeout-minutes: 20

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 9.12.0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'pnpm'
        cache-dependency-path: 'pnpm-lock.yaml'

    - name: Cache dependencies and build artifacts
      uses: actions/cache@v4
      with:
        path: |
          ~/.pnpm-store
          apps/sb-server/dist
          packages/zero-schema/dist
          node_modules/.cache
        key: ${{ runner.os }}-sb-server-${{ hashFiles('**/pnpm-lock.yaml', 'apps/sb-server/src/**/*', 'packages/zero-schema/src/**/*') }}-${{ github.sha }}
        restore-keys: |
          ${{ runner.os }}-sb-server-${{ hashFiles('**/pnpm-lock.yaml', 'apps/sb-server/src/**/*', 'packages/zero-schema/src/**/*') }}-
          ${{ runner.os }}-sb-server-

    - name: Install system dependencies
      run: sudo apt-get update && sudo apt-get install -y libreadline-dev

    - name: Download schema artifacts
      uses: actions/download-artifact@v4
      with:
        name: zero-schema-dist
        path: packages/zero-schema/dist

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Build with incremental compilation
      id: build
      run: |
        cd apps/sb-server
        
        # Check if we can use cached build
        if [ -f "dist/index.js" ] && [ -f "dist/index.js.map" ]; then
          echo "✅ Using cached build output"
          pnpm run setup-zero-schema
          echo "build_success=true" >> $GITHUB_OUTPUT
        else
          echo "🔨 Building from source..."
          if pnpm run build; then
            echo "✅ Build successful!"
            echo "build_success=true" >> $GITHUB_OUTPUT
          else
            echo "❌ Build failed"
            echo "build_success=false" >> $GITHUB_OUTPUT
            exit 1
          fi
        fi

    - name: Authenticate to Google Cloud
      if: steps.build.outputs.build_success == 'true'
      uses: 'google-github-actions/auth@v2'
      with:
        credentials_json: '${{ secrets.GOOGLE_CLOUD_CREDENTIALS }}'

    - name: Set up Cloud SDK
      if: steps.build.outputs.build_success == 'true'
      uses: 'google-github-actions/setup-gcloud@v2'
      with:
        version: 'latest'

    - name: Deploy with optimized retry
      if: steps.build.outputs.build_success == 'true'
      id: deploy
      run: |
        cd apps/sb-server
        
        ENVIRONMENT="${{ inputs.environment }}"
        MAX_RETRIES=2  # Reduced from 3
        RETRY_COUNT=0
        WAIT_TIME=10   # Reduced initial wait
        
        echo "🚀 Deploying SB Server to: $ENVIRONMENT"
        
        # Set deployment script based on environment
        case "$ENVIRONMENT" in
          preview)
            DEPLOY_SCRIPT="./deploy-preview-nodejs.sh"
            APP_URL="https://preview-dot-psychic-valve-439013-d2.lm.r.appspot.com"
            ;;
          staging)
            DEPLOY_SCRIPT="./deploy-staging-nodejs.sh"
            APP_URL="https://staging-dot-psychic-valve-439013-d2.lm.r.appspot.com"
            ;;
          production)
            DEPLOY_SCRIPT="./deploy-production-nodejs.sh"
            chmod +x "$DEPLOY_SCRIPT"
            APP_URL="https://psychic-valve-439013-d2.lm.r.appspot.com"
            ;;
        esac
        
        while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
          echo "🚀 Deployment attempt $((RETRY_COUNT + 1))/$MAX_RETRIES"
          
          if timeout 480s $DEPLOY_SCRIPT; then
            echo "✅ SB Server deployment successful!"
            echo "deployment_success=true" >> $GITHUB_OUTPUT
            echo "app_url=$APP_URL" >> $GITHUB_OUTPUT
            break
          else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
              echo "⚠️ Deployment failed, retrying in ${WAIT_TIME}s..."
              sleep $WAIT_TIME
              WAIT_TIME=$((WAIT_TIME * 2))  # Exponential backoff
            else
              echo "❌ All deployment attempts failed"
              echo "deployment_success=false" >> $GITHUB_OUTPUT
              exit 1
            fi
          fi
        done

    - name: Health check with timeout
      if: steps.deploy.outputs.deployment_success == 'true'
      run: |
        echo "🔍 Verifying SB Server deployment..."
        
        APP_URL="${{ steps.deploy.outputs.app_url }}"
        
        # Quick health check with reduced retries
        MAX_RETRIES=3
        RETRY_COUNT=0
        WAIT_TIME=10
        
        while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
          if curl -f --max-time 15 "$APP_URL/health" 2>/dev/null; then
            echo "✅ SB Server health check passed"
            break
          else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
              echo "⏳ Health check failed, retrying in ${WAIT_TIME}s..."
              sleep $WAIT_TIME
            else
              echo "⚠️ Health check failed after all retries, but deployment completed"
            fi
          fi
        done

    - name: Add deployment summary
      if: always()
      run: |
        echo "## 🚀 SB Server Deployment" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Environment:** ${{ inputs.environment }}" >> $GITHUB_STEP_SUMMARY
        echo "**Build Status:** ${{ steps.build.outputs.build_success == 'true' && '✅ Success' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
        echo "**Deployment Status:** ${{ steps.deploy.outputs.deployment_success == 'true' && '✅ Success' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        if [[ "${{ steps.deploy.outputs.deployment_success }}" == "true" ]]; then
          echo "**Service URL:** ${{ steps.deploy.outputs.app_url }}" >> $GITHUB_STEP_SUMMARY
        fi