#!/bin/bash

# AWS Lambda Video Rendering Setup Script
# This script sets up Remotion Lambda for video rendering

set -e

echo "🚀 Starting AWS Lambda Video Rendering Setup..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install it first:"
    echo "   https://nodejs.org/"
    exit 1
fi

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm is not installed. Please install it first:"
    echo "   npm install -g pnpm"
    exit 1
fi

echo "✅ Node.js and pnpm are available"

# Navigate to the web app directory


echo "📦 Installing Remotion Lambda dependencies..."
pnpm add @remotion/lambda@4.0.272 @remotion/lambda/client@4.0.272

echo "🏗️  Creating Remotion Lambda site..."
npx remotion lambda sites create components/video-editor/v7/remotion/lambda.tsx --site-name=axcels-video-studio --region=us-east-1

echo "⚡ Deploying Lambda function..."
npx remotion lambda functions deploy --region=us-east-1 --memory=2048 --disk=2048 --timeout=900

echo "✅ AWS Lambda setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Update your .env.local file with AWS credentials:"
echo "   AWS_ACCESS_KEY_ID=your_access_key"
echo "   AWS_SECRET_ACCESS_KEY=your_secret_key"
echo "   AWS_REGION=us-east-1"
echo ""
echo "2. The Lambda function name and bucket name should be automatically"
echo "   detected from the deployment output above."
echo ""
echo "3. Test the 'Render Video' button in your video studio!"
