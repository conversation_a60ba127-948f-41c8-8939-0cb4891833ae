import { Overlay, OverlayType } from "./types";

// Default and maximum number of rows to display in the editor
export const INITIAL_ROWS = 5;
export const MAX_ROWS = 12;
// Frames per second for video rendering
export const FPS = 30;

// Name of the component being tested/rendered
export const COMP_NAME = "TestComponent";
export const LAMBDA_COMP_NAME = "lambda-video";


// Video configuration
export const DURATION_IN_FRAMES = 30;
export const VIDEO_WIDTH = 1280; // 720p HD video dimensions
export const VIDEO_HEIGHT = 720;

// UI configuration
export const ROW_HEIGHT = 44; // Slightly increased from 48
export const SHOW_LOADING_PROJECT_ALERT = true; // Controls visibility of asset loading indicator
export const DISABLE_MOBILE_LAYOUT = false;

/**w
 * This constant disables video keyframe extraction in the browser. Enable this if you're working with
 * multiple videos or large video files to improve performance. Keyframe extraction is CPU-intensive and can
 * cause browser lag. For production use, consider moving keyframe extraction to the server side.
 * Future versions of Remotion may provide more efficient keyframe handling.
 */
export const DISABLE_VIDEO_KEYFRAMES = true;

// AWS deployment configuration
export const SITE_NAME = "axcels-video-studio";
export const LAMBDA_FUNCTION_NAME = "remotion-render-4-0-272-mem2048mb-disk2048mb-900sec";
export const REGION = "us-east-1";
export const BUCKET_NAME = "remotionlambda-useast1-9pa1u9l5q9";

// Zoom control configuration
export const ZOOM_CONSTRAINTS = {
  min: 0.2, // Minimum zoom level
  max: 10, // Maximum zoom level
  step: 0.1, // Smallest increment for manual zoom controls
  default: 1, // Default zoom level
  zoomStep: 0.15, // Zoom increment for zoom in/out buttons
  wheelStep: 0.3, // Zoom increment for mouse wheel
  transitionDuration: 100, // Animation duration in milliseconds
  easing: "cubic-bezier(0.4, 0.0, 0.2, 1)", // Smooth easing function for zoom transitions
};

// Timeline Snapping configuration
export const SNAPPING_CONFIG = {
  thresholdFrames: 1, // Default snapping sensitivity in frames
  enableVerticalSnapping: true, // Enable snapping to items in adjacent rows
};

// Add new constant for push behavior
export const ENABLE_PUSH_ON_DRAG = false; // Set to false to disable pushing items on drag

// Render configuration
// NOTE: TO CHANGE RENDER TYPE, UPDATE THE RENDER_TYPE CONSTANT
export type RenderType = "ssr" | "lambda";
export const RENDER_TYPE: RenderType = "lambda";

// Autosave configuration
export const AUTO_SAVE_INTERVAL = 60000; // Autosave every 60 seconds

export const DEFAULT_OVERLAYS: Overlay[] = [
  {
    left: 24,
    top: 127,
    width: 1195,
    height: 444,
    durationInFrames: 47,
    from: 0,
    id: crypto.randomUUID(),
    row: 0,
    rotation: 0,
    isDragging: false,
    type: OverlayType.TEXT,
    content: "",
    styles: {
      fontSize: "3rem",
      fontWeight: "700",
      color: "rgba(255, 255, 255, 1)",
      backgroundColor: "",
      fontFamily: "font-league-spartan",
      fontStyle: "normal",
      textDecoration: "none",
      lineHeight: "1.1",
      textAlign: "center",
      letterSpacing: "-0.03em",
      opacity: 1,
      zIndex: 1,
      transform: "none",
    },
  }
];
