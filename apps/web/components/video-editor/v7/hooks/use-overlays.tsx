import { useCallback, useState } from "react";
import { Overlay, OverlayType, CaptionStyles, CaptionOverlay } from "../types";
import { defaultCaptionStyles } from "../components/overlays/captions/caption-settings";
import { generateOverlayId } from "../utils/uuid";
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

// No conversion functions needed - database schema now matches frontend exactly!

/**
 * Hook to manage overlay elements in the editor using Zero sync
 * Overlays are stored in the database and synced in real-time
 * @returns Object containing overlay state and management functions
 */
export const useOverlays = () => {
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  
  // Get the selected scene ID from user cache
  const [userCacheResults] = useZeroQuery(
    zero?.query.user_cache.where('user_id', '=', workspace.user?.id || ''),
    {
      ttl: '1d'
    }
  );
  const userCache = userCacheResults?.[0];
  const selectedSceneId = userCache?.selected_scene_id;
  // Get overlays from Zero sync for the current scene
  const [overlays] = useZeroQuery(
    zero?.query.video_project_overlays
      .where('scene_id', '=', selectedSceneId || '')
      .orderBy('created_at', 'asc') as any,
    {
      ttl: '5m'
    }
  ) as [Overlay[], any];
  
  // Overlays are now directly usable - no conversion needed!

  // Tracks which overlay is currently selected for editing
  const [selectedOverlayId, setSelectedOverlayId] = useState<string | null>(
    null
  );

  /**
   * Updates properties of a specific overlay using Zero mutations
   * Supports both direct property updates and functional updates
   * @example
   * // Direct update
   * changeOverlay(1, { width: 100 })
   * // Functional update
   * changeOverlay(1, (overlay) => ({ ...overlay, width: overlay.width + 10 }))
   */
  const changeOverlay = useCallback(
    (
      overlayId: string,
      updater: Partial<Overlay> | ((overlay: Overlay) => Overlay)
    ) => {
      if (!zero || !selectedSceneId) return;

      const currentOverlay = overlays?.find(o => o.id === overlayId);
      if (!currentOverlay) return;
      const updatedOverlay = typeof updater === "function"
        ? updater(currentOverlay)
        : ({ ...currentOverlay, ...updater } as Overlay);

      
      try {
       
        // Use Zero's built-in update mutation - no await needed for local DB
        (zero.mutate.video_project_overlays as any).update({
          id: overlayId,
          values: {
          ...updatedOverlay,
          scene_id: selectedSceneId,
          project_id: userCache?.selected_video_project,
          updated_at: Date.now(),
          created_at: Date.now(),
          },
        });
      } catch (error) {
        console.error("Failed to update overlay:", error);
      }
    },
    [zero, selectedSceneId, overlays]
  );

  /**
   * Adds a new overlay to the editor using Zero mutations
   * Automatically generates a new unique ID and stores it in the database
   */
  const addOverlay = useCallback((newOverlay: Omit<Overlay, "id">) => {
    if (!zero || !selectedSceneId) return;

    console.log({newOverlay, });
    console.log({userCache });
    const newId = crypto.randomUUID();
    const overlayWithId = { 
      ...newOverlay, 
      id: newId,
      scene_id: selectedSceneId,
      project_id: userCache?.selected_video_project, // For compatibility
      created_at: Date.now() as any,
      updated_at: Date.now() as any,
    } as Overlay;

    console.log({overlayWithId });
    try {
      // Use Zero's built-in insert mutation - no await needed for local DB
      
      (zero.mutate.video_project_overlays as any).insert(overlayWithId);
      setSelectedOverlayId(newId);
    } catch (error) {
      console.error("Failed to add overlay:", error);
    }
  }, [zero, selectedSceneId]);

  /**
   * Removes an overlay by its ID using Zero mutations
   */
  const deleteOverlay = useCallback((id: string) => {
    if (!zero) return;

    try {
      // Use Zero's built-in delete mutation - no await needed for local DB
      (zero.mutate.video_project_overlays as any).delete({ id });
      setSelectedOverlayId(null);
    } catch (error) {
      console.error("Failed to delete overlay:", error);
    }
  }, [zero]);

  /**
   * Removes all overlays on a specified row using Zero mutations
   * @param row The row number to clear
   */
  const deleteOverlaysByRow = useCallback((row: number) => {
    if (!zero || !overlays) return;

    const overlaysToDelete = overlays.filter((overlay) => overlay.row === row);
    
    try {
      // Delete all overlays in the row
      overlaysToDelete.forEach(overlay => 
        (zero.mutate.video_project_overlays as any).delete({ id: overlay.id })
      );
      setSelectedOverlayId(null);
    } catch (error) {
      console.error("Failed to delete overlays by row:", error);
    }
  }, [zero, overlays]);

  /**
   * Creates a copy of an existing overlay using Zero mutations
   * The duplicated overlay is positioned immediately after the original in the timeline
   */
  const duplicateOverlay = useCallback((id: string) => {
    if (!zero || !selectedSceneId || !overlays) return;

    const overlayToDuplicate = overlays.find((overlay) => overlay.id === id);
    if (!overlayToDuplicate) return;

    const newId = generateOverlayId();

    // Find any overlays that would overlap with the duplicated position
    const overlaysInRow = overlays.filter(
      (o) => o.row === overlayToDuplicate.row && o.id !== id
    );

    // Calculate initial position for duplicate
    let newFrom = overlayToDuplicate.from + overlayToDuplicate.durationInFrames;

    // Check for overlaps and adjust position if needed
    let hasOverlap = true;
    while (hasOverlap) {
      hasOverlap = overlaysInRow.some((existingOverlay) => {
        const duplicateEnd = newFrom + overlayToDuplicate.durationInFrames;
        const existingEnd = existingOverlay.from + existingOverlay.durationInFrames;

        // Check for any overlap
        return (
          (newFrom >= existingOverlay.from && newFrom < existingEnd) ||
          (duplicateEnd > existingOverlay.from && duplicateEnd <= existingEnd) ||
          (newFrom <= existingOverlay.from && duplicateEnd >= existingEnd)
        );
      });

      if (hasOverlap) {
        // If there's an overlap, try positioning after the last overlay in the row
        const lastOverlay = [...overlaysInRow].sort(
          (a, b) => b.from + b.durationInFrames - (a.from + a.durationInFrames)
        )[0];
        newFrom = lastOverlay
          ? lastOverlay.from + lastOverlay.durationInFrames + 1
          : newFrom + 1;
      }
    }

    const duplicatedOverlay: Overlay = {
      ...overlayToDuplicate,
      id: newId,
      from: newFrom,
      scene_id: selectedSceneId,
      project_id: userCache?.selected_video_project || '',
      created_at: Date.now() as any,
      updated_at: Date.now() as any,
    };

    try {
      // Use Zero's built-in insert mutation
      console.log('233323', {duplicatedOverlay });
      (zero.mutate.video_project_overlays as any).insert(duplicatedOverlay);
    } catch (error) {
      console.error("Failed to duplicate overlay:", error);
    }
  }, [zero, selectedSceneId, overlays]);

  /**
   * Splits an overlay into two separate overlays at a specified frame using Zero mutations
   * Useful for creating cuts or transitions in video/audio content
   * @example
   * // Split an overlay at frame 100
   * splitOverlay(1, 100)
   */
  const splitOverlay = useCallback((id: string, splitFrame: number) => {
    if (!zero || !selectedSceneId || !overlays) return;

    const fps = 30; // Make this configurable
    const msPerFrame = 1000 / fps;

    console.log("=== Starting Caption Split Operation ===");
    console.log(
      `Split requested at frame ${splitFrame} (${splitFrame * msPerFrame}ms)`
    );

    const overlayToSplit = overlays.find((overlay) => overlay.id === id);
    if (!overlayToSplit) {
      console.log("Overlay not found:", id);
      return;
    }

    // Validate split point
    if (
      splitFrame <= overlayToSplit.from ||
      splitFrame >= overlayToSplit.from + overlayToSplit.durationInFrames
    ) {
      console.warn("Invalid split point");
      return;
    }

    const firstPartDuration = splitFrame - overlayToSplit.from;
    const secondPartDuration = overlayToSplit.durationInFrames - firstPartDuration;
    const newId = generateOverlayId();

    // Calculate start times for media overlays
    const secondHalfStartTime = calculateSecondHalfStartTime(
      overlayToSplit,
      firstPartDuration
    );

    // Create split overlays
    const [firstHalf, secondHalf] = createSplitOverlays(
      overlayToSplit,
      newId,
      splitFrame,
      firstPartDuration,
      secondPartDuration,
      secondHalfStartTime
    );

    try {
      // Update the first half (existing overlay)
      (zero.mutate.video_project_overlays as any).update({
        id: id,
        values: {
          ...firstHalf,
          scene_id: selectedSceneId,
          project_id: userCache?.selected_video_project || '',
          updated_at: Date.now(),
        },
      });

      // Insert the second half (new overlay)
      (zero.mutate.video_project_overlays as any).insert({
        ...secondHalf,
        scene_id: selectedSceneId,
        project_id: userCache?.selected_video_project || '',
        created_at: Date.now(),
        updated_at: Date.now(),
      });
    } catch (error) {
      console.error("Failed to split overlay:", error);
    }
  }, [zero, selectedSceneId, overlays]);

  const updateOverlayStyles = useCallback(
    (overlayId: string, styles: Partial<CaptionStyles>) => {
      changeOverlay(overlayId, (overlay) => {
        if (overlay.type !== OverlayType.CAPTION) return overlay;
        return {
          ...overlay,
          styles: {
            ...(overlay.styles || defaultCaptionStyles),
            ...styles,
          },
        };
      });
    },
    [changeOverlay]
  );

  const resetOverlays = useCallback(() => {
    if (!zero || !selectedSceneId || !overlays) return;

    try {
      // Delete all overlays for the current scene
      overlays.forEach(overlay => 
        (zero.mutate.video_project_overlays as any).delete({ id: overlay.id })
      );
      setSelectedOverlayId(null);
    } catch (error) {
      console.error("Failed to reset overlays:", error);
    }
  }, [zero, selectedSceneId, overlays]);

  // Implement setOverlays using Zero sync - replaces all overlays for the current scene
  const setOverlays = useCallback(
    (newOverlays: Overlay[]) => {
      if (!zero || !selectedSceneId) return;

      try {
        // First, delete all existing overlays for this scene
        if (overlays && overlays.length > 0) {
          overlays.forEach(overlay =>
            (zero.mutate.video_project_overlays as any).delete({
              id: overlay.id,
            })
          );
        }

        // Then insert all new overlays
        if (newOverlays.length > 0) {
          console.log("setting overlays", {newOverlays });
          newOverlays.forEach(overlay =>
            (zero.mutate.video_project_overlays as any).insert({
              id: overlay.id,
              scene_id: selectedSceneId,
              project_id: userCache?.selected_video_project || '', // For compatibility
              type: overlay.type,
              durationInFrames: overlay.durationInFrames,
              from: overlay.from,
              height: overlay.height,
              row: overlay.row,
              left: overlay.left,
              top: overlay.top,
              width: overlay.width,
              isDragging: overlay.isDragging || false,
              rotation: overlay.rotation,
              loading: overlay.loading || false,
              content: (overlay as any).content || '',
              src: (overlay as any).src || '',
              styles: overlay.styles || {},
              transcript: (overlay as any).transcript,
              videoStartTime: (overlay as any).videoStartTime,
              startFromSound: (overlay as any).startFromSound,
              speed: (overlay as any).speed,
              duration: (overlay as any).duration,
              captions: (overlay as any).captions,
              template: (overlay as any).template,
              category: (overlay as any).category,
            })
          );
        }
      } catch (error) {
        console.error("Failed to set overlays:", error);
      }
    },
    [zero, selectedSceneId, overlays]
  );

  return {
    overlays,
    selectedOverlayId,
    setSelectedOverlayId,
    changeOverlay,
    addOverlay,
    deleteOverlay,
    deleteOverlaysByRow,
    duplicateOverlay,
    splitOverlay,
    updateOverlayStyles,
    resetOverlays,
    setOverlays,
  };
};

/**
 * Calculates the starting time for the second half of a split media overlay
 * For clips and sounds, we need to adjust their internal start times
 * to maintain continuity after the split
 */
const calculateSecondHalfStartTime = (
  overlay: Overlay,
  firstPartDuration: number
): number => {
  if (overlay.type === OverlayType.VIDEO) {
    return (overlay.videoStartTime || 0) + firstPartDuration;
  }
  if (overlay.type === OverlayType.SOUND) {
    return (overlay.startFromSound || 0) + firstPartDuration;
  }
  return 0;
};

/**
 * Creates two new overlay objects from an original overlay when splitting
 * The first half maintains the original ID and timing
 * The second half gets a new ID and adjusted timing properties
 * Preserves all other properties from the original overlay
 */
const createSplitOverlays = (
  original: Overlay,
  newId: string,
  splitFrame: number,
  firstPartDuration: number,
  secondPartDuration: number,
  secondHalfStartTime: number
): [Overlay, Overlay] => {
  const fps = 30;
  const msPerFrame = 1000 / fps;
  const splitTimeMs = splitFrame * msPerFrame;

  if (original.type === OverlayType.CAPTION) {
    // Calculate absolute time ranges for both splits
    const originalStartMs = original.from * msPerFrame;
    const splitOffsetMs = splitTimeMs - originalStartMs; // Time relative to overlay start

    console.log("🎯 Split Timing Calculations:", {
      originalStartMs,
      splitTimeMs,
      splitOffsetMs,
      originalCaptions: original.captions.map((c) => ({
        text: c.text,
        startMs: c.startMs,
        endMs: c.endMs,
      })),
    });

    // Split captions at word level, keeping timestamps relative to their overlay
    const firstHalfCaptions = original.captions
      .filter((caption) => caption.startMs < splitOffsetMs)
      .map((caption) => ({
        ...caption,
        endMs: Math.min(caption.endMs, splitOffsetMs),
        words: caption.words
          .filter((word) => word.startMs < splitOffsetMs)
          .map((word) => ({
            ...word,
            endMs: Math.min(word.endMs, splitOffsetMs),
          })),
      }))
      .filter((caption) => caption.words.length > 0)
      .map((caption) => ({
        ...caption,
        text: caption.words.map((w) => w.word).join(" "),
      }));

    const secondHalfCaptions = original.captions
      .filter((caption) => caption.endMs > splitOffsetMs)
      .map((caption) => ({
        ...caption,
        startMs: Math.max(0, caption.startMs - splitOffsetMs),
        endMs: caption.endMs - splitOffsetMs,
        words: caption.words
          .filter((word) => word.endMs > splitOffsetMs)
          .map((word) => ({
            ...word,
            startMs: Math.max(0, word.startMs - splitOffsetMs),
            endMs: word.endMs - splitOffsetMs,
          })),
      }))
      .filter((caption) => caption.words.length > 0)
      .map((caption) => ({
        ...caption,
        text: caption.words.map((w) => w.word).join(" "),
      }));

    console.log("📑 Split Results:", {
      firstHalf: {
        captionCount: firstHalfCaptions.length,
        captions: firstHalfCaptions.map((c) => ({
          text: c.text,
          startMs: c.startMs,
          endMs: c.endMs,
          wordCount: c.words.length,
        })),
      },
      secondHalf: {
        captionCount: secondHalfCaptions.length,
        captions: secondHalfCaptions.map((c) => ({
          text: c.text,
          startMs: c.startMs,
          endMs: c.endMs,
          wordCount: c.words.length,
        })),
      },
    });

    // Create the split overlays with adjusted captions
    const firstHalf: CaptionOverlay = {
      ...original,
      durationInFrames: firstPartDuration,
      captions: firstHalfCaptions,
    };

    const secondHalf: CaptionOverlay = {
      ...original,
      id: newId,
      from: splitFrame,
      durationInFrames: secondPartDuration,
      captions: secondHalfCaptions,
    };

    return [firstHalf, secondHalf];
  }

  const firstHalf: Overlay = {
    ...original,
    durationInFrames: firstPartDuration,
  };

  const secondHalf: Overlay = {
    ...original,
    id: newId,
    from: splitFrame,
    durationInFrames: secondPartDuration,
    ...(original.type === OverlayType.VIDEO && {
      videoStartTime: secondHalfStartTime,
    }),
    ...(original.type === OverlayType.SOUND && {
      startFromSound: secondHalfStartTime,
    }),
  };

  return [firstHalf, secondHalf];
};
