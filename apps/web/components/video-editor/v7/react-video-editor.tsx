"use client";

// UI Components
import { SimpleSidebar } from "./components/sidebar/simple-sidebar";
import { Editor } from "./components/core/editor";
import { SidebarProvider as EditorSidebarProvider } from "./contexts/sidebar-context";

// Context Providers
import { EditorProvider } from "./contexts/editor-context";

// Custom Hooks
import { useOverlays } from "./hooks/use-overlays";
import { useVideoPlayer } from "./hooks/use-video-player";
import { useTimelineClick } from "./hooks/use-timeline-click";
import { useAspectRatio } from "./hooks/use-aspect-ratio";
import { useCompositionDuration } from "./hooks/use-composition-duration";
import { useHistory } from "./hooks/use-history";

// Types
import { Overlay } from "./types";
import { useRendering } from "./hooks/use-rendering";
import { FPS, RENDER_TYPE, COMP_NAME, LAMBDA_COMP_NAME } from "./constants";
import { TimelineProvider } from "./contexts/timeline-context";

import { useState, useEffect, useRef } from "react";
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { LocalMediaProvider } from "./contexts/local-media-context";
import { KeyframeProvider } from "./contexts/keyframe-context";
import { AssetLoadingProvider } from "./contexts/asset-loading-context";

export default function ReactVideoEditor({ projectId }: { projectId: string }) {

  // Overlay management hooks
  const {
    overlays,
    selectedOverlayId,
    setSelectedOverlayId,
    changeOverlay,
    addOverlay,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,
    deleteOverlaysByRow,
    updateOverlayStyles,
    resetOverlays,
    setOverlays,
  } = useOverlays();

    // Video player controls and state
    const { isPlaying, currentFrame, playerRef, togglePlayPause, formatTime } =
    useVideoPlayer();

  // Composition duration calculations
  const { durationInFrames, durationInSeconds } =
    useCompositionDuration(overlays);

  // Aspect ratio and player dimension fmanagement
  const {
    aspectRatio,
    setAspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
  } = useAspectRatio();

  const { width: compositionWidth, height: compositionHeight } =
  getAspectRatioDimensions();

  // Ensure baseUrl is properly set for Lambda rendering
  const baseUrl = RENDER_TYPE === "lambda"
    ? process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"
    : undefined;

  console.log(`🎬 [EDITOR] Render type: ${RENDER_TYPE}, baseUrl: ${baseUrl}`);
  console.log(`🎬 [EDITOR] Overlays count: ${overlays?.length || 0}`);

  const inputProps = {
    overlays,
    durationInFrames,
    fps: FPS,
    width: compositionWidth,
    height: compositionHeight,
    src: "",
    baseUrl,
  };

  const { renderMedia, state } = useRendering(
    RENDER_TYPE === "lambda" ? LAMBDA_COMP_NAME : COMP_NAME,
    inputProps,
    RENDER_TYPE
  );

  // Note: History management removed since overlays are now managed by Zero sync
  // The database provides persistence and real-time sync instead
  const canUndo = false;
  const canRedo = false;
  const undo = () => {};
  const redo = () => {};

  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const [playbackRate, setPlaybackRate] = useState(1);

  // Get user cache to track project and scene changes
  const [userCacheResults] = useZeroQuery(
    zero?.query.user_cache.where('user_id', '=', workspace.user?.id || ''),
    {
      ttl: '1d'
    }
  );
  const userCache = userCacheResults?.[0];
  const selectedSceneId = userCache?.selected_scene_id;

  // Load scenes for the project
  const [projectScenes] = useZeroQuery(
    zero?.query.video_project_scenes
      .where("project_id",'=', projectId)
      .orderBy("order_index", "asc") as any
  ) as [any[], any];


  // Event handlers
  const handleOverlayChange = (updatedOverlay: Overlay) => {
    changeOverlay(updatedOverlay.id, () => updatedOverlay);
  };

  const handleTimelineClick = useTimelineClick(playerRef as any, durationInFrames);

  // Handle edge case: if no scene is selected but scenes exist, select the first one
  useEffect(() => {
    if (!selectedSceneId && projectScenes && projectScenes.length > 0 && zero && workspace.user?.id) {
      const firstScene = projectScenes[0];
      (zero.mutate.user_cache as any).update({
        user_id: workspace.user.id,
        values: {
          selected_scene_id: firstScene.id,
        }
      });
    }
  }, [selectedSceneId, projectScenes, zero, workspace.user?.id]);

  // Handle scene changes - load scene properties (overlays are automatically loaded by useOverlays)
  useEffect(() => {
    if (!selectedSceneId || !projectScenes?.length) return;

    const currentScene = projectScenes.find(scene => scene.id === selectedSceneId);
    if (!currentScene) return;

    // Load scene properties - overlays are handled automatically by Zero sync
    setAspectRatio(currentScene.aspect_ratio || "16:9");

    // Use scene dimensions or defaults
    const width = currentScene.width && currentScene.width > 0 ? currentScene.width : 1280;
    const height = currentScene.height && currentScene.height > 0 ? currentScene.height : 720;
    updatePlayerDimensions(width, height);

  }, [selectedSceneId, projectScenes]);

  // Combine all editor context values
  const editorContextValue = {
    // Overlay management
    overlays,
    selectedOverlayId,
    setSelectedOverlayId,
    changeOverlay,
    handleOverlayChange,
    addOverlay,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,
    resetOverlays,

    // Player controls
    isPlaying,
    currentFrame,
    playerRef,
    togglePlayPause,
    formatTime,
    handleTimelineClick,
    playbackRate,
    setPlaybackRate,

    // Dimensions and duration
    aspectRatio,
    setAspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
    durationInFrames,
    durationInSeconds,

    // Add renderType to the context
    renderType: RENDER_TYPE,
    renderMedia,
    state,

    deleteOverlaysByRow,

    // History management
    undo,
    redo,
    canUndo,
    canRedo,

    // New style management
    updateOverlayStyles,

    // Batch overlay operations
    setOverlays,
  };

  return (
    <EditorSidebarProvider>
      <KeyframeProvider>
        <TimelineProvider>
          <EditorProvider value={editorContextValue}>
            <LocalMediaProvider>
              <AssetLoadingProvider>
                {/* Simple flex layout like Image Studio - no conflicting providers */}
                <div className="flex h-full w-full">
                  {/* Video Editor Tools Sidebar */}
                  <div className="w-[350px] shrink-0 border-r bg-background">
                    <SimpleSidebar />
                  </div>

                  {/* Main Editor Content */}
                  <div className="flex-1">
                    <Editor
                      projectId={projectId}
                    />
                  </div>
                </div>


              </AssetLoadingProvider>
            </LocalMediaProvider>
          </EditorProvider>
        </TimelineProvider>
      </KeyframeProvider>
    </EditorSidebarProvider>
  );
}
