"use client";

import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export interface Scene {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  order_index?: number;
  duration_frames?: number;
  aspect_ratio?: string;
  created_at?: number;
  updated_at?: number;
}

interface SceneSelectionProps {
  projectId: string;
  className?: string;
}

/**
 * SceneSelection Component
 * 
 * @component
 * @description
 * A tab-like interface for managing video scenes. Features:
 * - Tab structure that blends into the timeline controls
 * - Active tab has no bottom border to create seamless connection
 * - Default scene with ability to add new scenes
 * - Sequential scene numbering (Scene 1, Scene 2, etc.)
 * 
 * @param {SceneSelectionProps} props - The component props
 * @param {string} [props.className] - Additional CSS classes
 */
export const SceneSelection: React.FC<SceneSelectionProps> = ({ 
  projectId,
  className
}) => {
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  
  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [sceneToDelete, setSceneToDelete] = useState<string | null>(null);
  
  // State for inline editing
  const [editingSceneId, setEditingSceneId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState<string>('');
  const editInputRef = useRef<HTMLInputElement>(null);

  // Load user cache to get selected scene
  const [userCache] = useZeroQuery(
    zero?.query.user_cache
      .where("user_id", "=", workspace.user?.id || '') as any,
    {
      ttl: '1m'
    }
  );

  const [projectScenes] = useZeroQuery(
    zero?.query.video_project_scenes
      .where("project_id", "=", userCache?.[0]?.selected_video_project || '')
      .orderBy("order_index", "asc") as any,
    {
      ttl: '1m'
    }
  );

  const createScene = async (data: any) => {
    if (!zero) return;
    
    try {
      await (zero.mutate.video_project_scenes as any).insert({
        id: data.id,
        project_id: data.project_id,
        user_id: workspace.user?.id,
        name: data.name,
        description: data.description,
        duration_frames: data.duration_frames,
        aspect_ratio: data.aspect_ratio,
        order_index: data.order_index
      });
    } catch (error) {
      console.error('Failed to create scene:', error);
      throw error;
    }
  };

  const deleteScene = async (sceneId: string) => {
    if (!zero) return;
    
    try {
      await (zero.mutate.video_project_scenes as any).delete({
        id: sceneId,
      });
    } catch (error) {
      console.error('Failed to delete scene:', error);
      throw error;
    }
  };

  const updateScene = async (sceneId: string, updates: Partial<Scene>) => {
    if (!zero) return;
    
    try {
      await (zero.mutate.video_project_scenes as any).update({
        id: sceneId,
        values: {
          ...updates,
          updated_at: Date.now(),
        }
      });
    } catch (error) {
      console.error('Failed to update scene:', error);
      throw error;
    }
  };
  
  const updateUserCacheSelectedScene = async (sceneId: string) => {
    if (!zero || !workspace.user?.id) return;
    
    try {
      if (userCache?.[0]) {
        // Update existing user cache
        await (zero.mutate.user_cache as any).update({
          user_id: workspace.user.id,
          values: {
            selected_scene_id: sceneId,
          }
        });
      } else {
        // Create new user cache entry
        await (zero.mutate.user_cache as any).insert({
          user_id: workspace.user.id,
          values: {
            selected_scene_id: sceneId,
          }
        });
      }
    } catch (error) {
      console.error('Failed to update user cache:', error);
      throw error;
    }
  };


  // @ts-expect-error projectScenes is an array
  const activeSceneId = projectScenes?.find(scene => scene?.id === userCache?.[0]?.selected_scene_id)?.id || projectScenes?.[0]?.id;

  // Focus input when editing starts
  useEffect(() => {
    if (editingSceneId && editInputRef.current) {
      editInputRef.current.focus();
      editInputRef.current.select();
    }
  }, [editingSceneId]);

  /**
   * Adds a new scene to the database
   * Automatically generates sequential scene names
   */
  const addNewScene = async () => {
    if (!zero || !workspace.user?.id) return;

    const newSceneNumber = ((projectScenes as any[])?.length || 0) + 1;
    const newSceneId = crypto.randomUUID();
    
    try {
      await createScene({
        id: newSceneId,
        project_id: projectId,
        name: `Scene ${newSceneNumber}`,
        order_index: newSceneNumber,
        created_at: Date.now(),
        updated_at: Date.now(),
      });
      
      // Switch to the new scene
      handleSceneSelect(newSceneId);
    } catch (error) {
      console.error('Failed to create new scene:', error);
    }
  };

  /**
   * Handles scene selection
   */
  const handleSceneSelect = async (sceneId: string) => {
    // Update user cache with selected scene
    try {
      await updateUserCacheSelectedScene(sceneId);
    } catch (error) {
      console.error('Failed to update selected scene:', error);
    }
  };

  /**
   * Initiates the delete scene process by opening confirmation dialog
   */
  const handleDeleteScene = (sceneId: string, event: React.MouseEvent) => {
    // Prevent the scene selection from being triggered
    event.stopPropagation();
    
    // Don't allow deletion if it's the last scene
    if ((projectScenes as any[])?.length <= 1) {
      return;
    }
    
    setSceneToDelete(sceneId);
    setDeleteDialogOpen(true);
  };

  /**
   * Confirms and executes the scene deletion
   */
  const confirmDeleteScene = async () => {
    if (!sceneToDelete) return;
    
    const scenesToDelete = sceneToDelete;
    const isActiveScene = scenesToDelete === activeSceneId;
    
    try {
      await deleteScene(scenesToDelete);
      
      // If we deleted the active scene, switch to the first remaining scene
      if (isActiveScene) {
        const remainingScenes = (projectScenes as any[])?.filter(scene => scene.id !== scenesToDelete);
        if (remainingScenes?.length > 0) {
          await handleSceneSelect(remainingScenes[0].id);
        }
      }
    } catch (error) {
      console.error('Failed to delete scene:', error);
    } finally {
      setDeleteDialogOpen(false);
      setSceneToDelete(null);
    }
  };

  /**
   * Cancels the scene deletion
   */
  const cancelDeleteScene = () => {
    setDeleteDialogOpen(false);
    setSceneToDelete(null);
  };

  /**
   * Starts editing a scene name
   */
  const startEditingScene = (sceneId: string, currentName: string) => {
    setEditingSceneId(sceneId);
    setEditingName(currentName);
  };

  /**
   * Saves the edited scene name
   */
  const saveSceneName = async () => {
    if (!editingSceneId || !editingName.trim()) {
      cancelEditingScene();
      return;
    }

    try {
      await updateScene(editingSceneId, { name: editingName.trim() });
      setEditingSceneId(null);
      setEditingName('');
    } catch (error) {
      console.error('Failed to update scene name:', error);
      // Keep editing mode on error
    }
  };

  /**
   * Cancels editing a scene name
   */
  const cancelEditingScene = () => {
    setEditingSceneId(null);
    setEditingName('');
  };

  /**
   * Handles key press events in the editing input
   */
  const handleEditingKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      saveSceneName();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      cancelEditingScene();
    }
  };

  return (
    <div className={cn("", className)}>
      <div className="flex items-end">
        {/* Scene Tabs */}
        {(projectScenes as any[])?.map((scene) => {

          const isActive = scene.id === activeSceneId;
          const canDelete = (projectScenes as any[])?.length > 1;
          const isEditing = editingSceneId === scene.id;
          
          return (
            <div
              key={scene.id}
              className={cn(
                "relative group",
                "border-l border-r border-t border-gray-200 dark:border-gray-700",
                "rounded-t-lg",
                isActive ? [
                  "bg-white/95 dark:bg-gray-900/30 backdrop-blur-sm",
                  "border-b-0 z-10",
                  // Add a bottom border to match TimelineControls background
                  "after:absolute after:bottom-0 after:left-0 after:right-0 after:h-px",
                  "after:bg-white/95 dark:after:bg-gray-900/30"
                ] : [
                  "bg-gray-200/80 dark:bg-gray-700/80",
                  "border-b border-gray-200 dark:border-gray-700",
                  "hover:bg-gray-300/80 dark:hover:bg-gray-600/80"
                ],
                // Ensure proper border connections
                "first:border-l last:border-r"
              )}
            >
              {isEditing ? (
                // Editing mode - show input field
                <div className="px-6 py-3">
                  <Input
                    ref={editInputRef}
                    value={editingName}
                    onChange={(e) => setEditingName(e.target.value)}
                    onKeyDown={handleEditingKeyPress}
                    onBlur={saveSceneName}
                    className={cn(
                      "h-auto py-0 px-0 text-sm font-medium border-none shadow-none",
                      "bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0",
                      isActive ? [
                        "text-gray-900 dark:text-white",
                      ] : [
                        "text-gray-700 dark:text-gray-300",
                      ]
                    )}
                  />
                </div>
              ) : (
                // Normal mode - show button
                <button
                  onClick={() => handleSceneSelect(scene.id)}
                  onDoubleClick={() => startEditingScene(scene.id, scene.name)}
                  className={cn(
                    "w-full px-6 py-3 text-sm font-medium transition-colors relative",
                    "text-left",
                    isActive ? [
                      "text-gray-900 dark:text-white",
                    ] : [
                      "text-gray-700 dark:text-gray-300",
                    ],
                    // Add padding-right for delete button when it's visible
                    canDelete && "pr-10"
                  )}
                  title="Double-click to rename"
                >
                  {scene.name}
                </button>
              )}
              
              {/* Delete Button - only show if more than one scene exists and not editing */}
              {canDelete && !isEditing && (
                <button
                  onClick={(e) => handleDeleteScene(scene.id, e)}
                  className={cn(
                    "absolute top-1 right-1 p-1 rounded",
                    "opacity-0 group-hover:opacity-100 transition-opacity",
                    "hover:bg-red-100 dark:hover:bg-red-900/20",
                    "text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                  )}
                  title="Delete scene"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </div>
          );
        })}
        
        {/* Add New Scene Button */}
        <Button
          onClick={addNewScene}
          variant="ghost"
          size="sm"
          className={cn(
            "px-4 py-3 text-gray-500 dark:text-gray-400",
            "hover:text-gray-700 dark:hover:text-gray-200",
            "bg-gray-200/80 dark:bg-gray-700/80",
            "hover:bg-gray-300/80 dark:hover:bg-gray-600/80",
            "border-l border-r border-t border-b border-gray-200 dark:border-gray-700",
            "rounded-t-lg",
            "transition-colors"
          )}
          title="Add new scene"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Scene</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure? This is irreversible and will permanently delete this scene and all its content.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteScene}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteScene}
              className="bg-red-600 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700"
            >
              Delete Scene
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
