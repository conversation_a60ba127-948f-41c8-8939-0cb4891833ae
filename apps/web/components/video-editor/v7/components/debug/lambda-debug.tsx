import React from 'react';
import { AbsoluteFill } from 'remotion';

interface LambdaDebugProps {
  overlays: any[];
  baseUrl?: string;
  isLambdaRender?: boolean;
  durationInFrames: number;
  width: number;
  height: number;
}

/**
 * Debug component to help diagnose Lambda rendering issues
 * This component logs detailed information about the rendering environment
 */
export const LambdaDebug: React.FC<LambdaDebugProps> = ({
  overlays,
  baseUrl,
  isLambdaRender,
  durationInFrames,
  width,
  height,
}) => {
  // Log environment information
  console.log('🐛 [LAMBDA-DEBUG] Environment Info:', {
    isLambdaRender,
    baseUrl,
    overlaysCount: overlays?.length || 0,
    durationInFrames,
    width,
    height,
    nodeEnv: process.env.NODE_ENV,
    nextPublicBaseUrl: process.env.NEXT_PUBLIC_BASE_URL,
    awsLambdaFunctionName: process.env.AWS_LAMBDA_FUNCTION_NAME,
    remotionLambda: process.env.REMOTION_LAMBDA,
  });

  // Log detailed overlay information
  if (overlays && overlays.length > 0) {
    overlays.forEach((overlay, index) => {
      console.log(`🐛 [LAMBDA-DEBUG] Overlay ${index}:`, {
        id: overlay.id,
        type: overlay.type,
        src: overlay.src,
        content: overlay.content,
        from: overlay.from,
        durationInFrames: overlay.durationInFrames,
        width: overlay.width,
        height: overlay.height,
        left: overlay.left,
        top: overlay.top,
        row: overlay.row,
        styles: overlay.styles,
        videoStartTime: overlay.videoStartTime,
        duration: overlay.duration,
      });

      // Check if video src is accessible
      if (overlay.type === 'video' && overlay.src) {
        console.log(`🐛 [LAMBDA-DEBUG] Video overlay ${index} src analysis:`, {
          originalSrc: overlay.src,
          isRelative: overlay.src.startsWith('/'),
          isAbsolute: overlay.src.startsWith('http'),
          resolvedUrl: overlay.src.startsWith('/') && baseUrl 
            ? `${baseUrl}${overlay.src}` 
            : overlay.src,
        });
      }
    });
  } else {
    console.warn('🐛 [LAMBDA-DEBUG] No overlays provided!');
  }

  // Render a simple debug overlay on the video
  return (
    <AbsoluteFill
      style={{
        backgroundColor: 'rgba(255, 0, 0, 0.1)', // Slight red tint to indicate debug mode
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '24px',
        color: 'white',
        textAlign: 'center',
        padding: '20px',
      }}
    >
      <div>
        <div>🐛 Lambda Debug Mode</div>
        <div style={{ fontSize: '16px', marginTop: '10px' }}>
          Overlays: {overlays?.length || 0} | BaseURL: {baseUrl || 'undefined'}
        </div>
        <div style={{ fontSize: '14px', marginTop: '5px' }}>
          Check console for detailed logs
        </div>
      </div>
    </AbsoluteFill>
  );
};
