import {
  OffthreadVideo,
  useCurrent<PERSON>rame,
  delayRender,
  continueRender,
} from "remotion";
import { ClipOverlay } from "../../../types";
import { animationTemplates } from "../../../templates/animation-templates";
import { toAbsoluteUrl } from "../../../utils/url-helper";
import { useEffect } from "react";

/**
 * Interface defining the props for the VideoLayerContent component
 */
interface VideoLayerContentProps {
  /** The overlay configuration object containing video properties and styles */
  overlay: ClipOverlay;
  /** The base URL for the video */
  baseUrl?: string;
}

/**
 * VideoLayerContent component renders a video layer with animations and styling
 *
 * This component handles:
 * - Video playback using Remotion's OffthreadVideo
 * - Enter/exit animations based on the current frame
 * - Styling including transform, opacity, border radius, etc.
 * - Video timing and volume controls
 *
 * @param props.overlay - Configuration object for the video overlay including:
 *   - src: Video source URL
 *   - videoStartTime: Start time offset for the video
 *   - durationInFrames: Total duration of the overlay
 *   - styles: Object containing visual styling properties and animations
 */
export const VideoLayerContent: React.FC<VideoLayerContentProps> = ({
  overlay,
  baseUrl,
}) => {
  const frame = useCurrentFrame();

  // Determine the video source URL with proper Lambda support
  let videoSrc = overlay.src;

  console.log(`🎬 [VIDEO-LAYER] Original src: ${overlay.src}, baseUrl: ${baseUrl}`);

  // If it's a relative URL and baseUrl is provided, use baseUrl
  if (overlay.src && overlay.src.startsWith("/") && baseUrl) {
    videoSrc = `${baseUrl}${overlay.src}`;
    console.log(`🎬 [VIDEO-LAYER] Using baseUrl resolution: ${videoSrc}`);
  }
  // Otherwise use the toAbsoluteUrl helper for relative URLs
  else if (overlay.src && overlay.src.startsWith("/")) {
    videoSrc = toAbsoluteUrl(overlay.src);
    console.log(`🎬 [VIDEO-LAYER] Using toAbsoluteUrl resolution: ${videoSrc}`);
  }
  // If it's already an absolute URL, use as-is
  else if (overlay.src && (overlay.src.startsWith("http://") || overlay.src.startsWith("https://"))) {
    videoSrc = overlay.src;
    console.log(`🎬 [VIDEO-LAYER] Using absolute URL: ${videoSrc}`);
  }
  // Fallback: if no valid src, log error
  else if (!overlay.src) {
    console.error(`🎬 [VIDEO-LAYER] No video src provided for overlay ${overlay.id}`);
    videoSrc = "";
  }

  useEffect(() => {
    console.log(`🎬 [VIDEO-LAYER] Preparing to load video: ${videoSrc}`);
    console.log(`🎬 [VIDEO-LAYER] Overlay data:`, {
      id: overlay.id,
      src: overlay.src,
      videoStartTime: overlay.videoStartTime,
      duration: overlay.duration,
      durationInFrames: overlay.durationInFrames,
      styles: overlay.styles
    });
    const handle = delayRender("Loading video");

    // Create a video element to preload the video
    const video = document.createElement("video");
    video.src = videoSrc;
    console.log(`🎬 [VIDEO-LAYER] Video src: ${videoSrc}`);
    const handleLoadedMetadata = () => {
      console.log(`✅ [VIDEO-LAYER] Video metadata loaded successfully: ${videoSrc}`);
      continueRender(handle);
    };

    const handleError = (error: ErrorEvent) => {
      console.error(`❌ [VIDEO-LAYER] Error loading video ${videoSrc}:`, error);
      continueRender(handle);
    };

    video.addEventListener("loadedmetadata", handleLoadedMetadata);
    video.addEventListener("error", handleError);

    return () => {
      video.removeEventListener("loadedmetadata", handleLoadedMetadata);
      video.removeEventListener("error", handleError);
      // Ensure we don't leave hanging render delays
      continueRender(handle);
    };
  }, [videoSrc]);

  // Calculate if we're in the exit phase (last 30 frames)
  const isExitPhase = frame >= overlay.durationInFrames - 30;

  // Apply enter animation only during entry phase
  const enterAnimation =
    !isExitPhase && 
    overlay.styles?.animation?.enter &&
    animationTemplates[overlay.styles.animation.enter]?.enter
      ? animationTemplates[overlay.styles.animation.enter]!.enter(
          frame,
          overlay.durationInFrames
        )
      : {};

  // Apply exit animation only during exit phase
  const exitAnimation =
    isExitPhase && 
    overlay.styles?.animation?.exit &&
    animationTemplates[overlay.styles.animation.exit]?.exit
      ? animationTemplates[overlay.styles.animation.exit]!.exit(
          frame,
          overlay.durationInFrames
        )
      : {};

  const videoStyle: React.CSSProperties = {
    width: "100%",
    height: "100%",
    objectFit: overlay.styles?.objectFit || "cover",
    opacity: overlay.styles?.opacity ?? 1,
    transform: overlay.styles?.transform || "none",
    borderRadius: overlay.styles?.borderRadius || "0px",
    filter: overlay.styles?.filter || "none",
    boxShadow: overlay.styles?.boxShadow || "none",
    border: overlay.styles?.border || "none",
    ...(isExitPhase ? exitAnimation : enterAnimation),
  };

  // Create a container style that includes padding and background color
  const containerStyle: React.CSSProperties = {
    width: "100%",
    height: "100%",
    padding: overlay.styles?.padding || "0px",
    backgroundColor: overlay.styles?.paddingBackgroundColor || "transparent",
    display: "flex", // Use flexbox for centering
    alignItems: "center",
    justifyContent: "center",
  };


  return (
    <div style={containerStyle}>
      <OffthreadVideo
        src={videoSrc || " "}
        startFrom={overlay.videoStartTime || 0}
        style={videoStyle}
        volume={overlay.styles?.volume ?? 1}
        playbackRate={overlay.speed ?? 1}
      />
    </div>
  );
};
