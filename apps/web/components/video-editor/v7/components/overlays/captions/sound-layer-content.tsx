import { Audio } from "remotion";
import { SoundOverlay } from "../../../types";
import { toAbsoluteUrl } from "../../../utils/url-helper";

interface SoundLayerContentProps {
  overlay: SoundOverlay;
  baseUrl?: string;
}

export const SoundLayerContent: React.FC<SoundLayerContentProps> = ({
  overlay,
  baseUrl,
}) => {
  // Determine the audio source URL
  let audioSrc = overlay.src || "";

  // Only process URL if it exists
  if (audioSrc) {
    // If it's a relative URL and baseUrl is provided, use baseUrl
    if (audioSrc.startsWith("/") && baseUrl) {
      audioSrc = `${baseUrl}${audioSrc}`;
    }
    // Otherwise use the toAbsoluteUrl helper for relative URLs
    else if (audioSrc.startsWith("/")) {
      audioSrc = toAbsoluteUrl(audioSrc);
    }
  }

  // Don't render Audio component if no valid src
  if (!audioSrc) {
    console.warn('SoundLayerContent: No valid audio src provided', overlay);
    return null;
  }

  return (
    <Audio
      src={audioSrc}
      startFrom={overlay.startFromSound || 0}
      volume={overlay.styles?.volume ?? 1}
    />
  );
};
