"use client";

import React, { useState, use<PERSON><PERSON>back, useMemo, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useQuery } from '@tanstack/react-query';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useZero } from '~/hooks/use-zero';
import { listAssetFolders, listFolderContents } from '~/services/storage';
import { AssetFolder, StorageFile } from '~/types/assets';
import { Loader2, File, Video, Image, Music, FileText, ArrowLeft } from 'lucide-react';
import { formatBytes } from '../../utils/format-utils';
import { useOverlays } from '../../hooks/use-overlays';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@kit/ui/breadcrumb";

// Types for the generate form
type VideoType =
  | "LinkedIn Ad"
  | "X Ad"
  | "Promo Video"
  | "Repurpose Video"
  | "Presentation"
  | "Animated Explainer"
  | "Technical Tutorial";

type SourceType = "Product Document" | "Asset Library" | "URL";

interface GenerateFormData {
  type: VideoType | null;
  length: string;
  sourceType: SourceType | null;
  selectedDocument: any | null;
  selectedFolder: string | null;
  selectedAsset: StorageFile | null;
  url: string;
  prompt: string;
  // For repurpose video
  selectedVideoAsset: any | null; // Asset from the assets table with transcript
}

const VIDEO_TYPES: { value: VideoType; label: string; disabled: boolean }[] = [
  { value: "Promo Video", label: "Promo Video", disabled: false },
  { value: "Repurpose Video", label: "Repurpose Video", disabled: false },
  { value: "LinkedIn Ad", label: "LinkedIn Ad (coming soon)", disabled: true },
  { value: "X Ad", label: "X Ad (coming soon)", disabled: true },
  { value: "Presentation", label: "Presentation (coming soon)", disabled: true },
  { value: "Animated Explainer", label: "Animated Explainer (coming soon)", disabled: true },
  { value: "Technical Tutorial", label: "Technical Tutorial (coming soon)", disabled: true }
];

/**
 * GeneratePanel Component
 *
 * Allows users to generate videos from various sources (URLs, PDFs, videos)
 * with different video types and lengths. Provides a comprehensive form
 * interface for video generation configuration.
 */
export const GeneratePanel: React.FC = () => {
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();
  const { setOverlays } = useOverlays();
  const [currentStep, setCurrentStep] = useState<'setup' | 'review'>('setup');
  const [generationResult, setGenerationResult] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [formData, setFormData] = useState<GenerateFormData>({
    type: null,
    length: "",
    sourceType: null,
    selectedDocument: null,
    selectedFolder: null,
    selectedAsset: null,
    url: "",
    prompt: "",
    selectedVideoAsset: null
  });
  const [assetFiles, setAssetFiles] = useState<StorageFile[]>([]);
  const [loadingAssets, setLoadingAssets] = useState(false);

  // Fetch asset folders
  const { data: assetFolders = [] } = useQuery<AssetFolder[]>({
    queryKey: ['assetFolders', workspace.account.id],
    queryFn: () => listAssetFolders(workspace.account.id),
    enabled: !!workspace.account.id,
  });

  // Fetch video/audio assets from the database for repurpose video
  const [assetsData] = useZeroQuery(
    zero?.query.assets.where('account_id', '=', workspace.account.id),
    {
      ttl: '5m' // Cache for 5 minutes
    }
  );

  // Get the selected scene ID from user cache (same pattern as useOverlays)
  const [userCacheResults] = useZeroQuery(
    zero?.query.user_cache.where('user_id', '=', workspace.user?.id || ''),
    {
      ttl: '1d'
    }
  );
  const userCache = userCacheResults?.[0];
  const selectedSceneId = userCache?.selected_scene_id;
  const selectedProjectId = userCache?.selected_video_project;

  // Filter for video and audio files
  const videoAudioAssets = assetsData?.filter(asset => 
    asset.file_type?.startsWith('video/') || asset.file_type?.startsWith('audio/')
  ) || [];

  // Load asset files when folder selection changes
  useEffect(() => {
    const loadAssetFiles = async () => {
      if (!formData.selectedFolder || formData.sourceType !== "Asset Library") {
        setAssetFiles([]);
        return;
      }

      try {
        setLoadingAssets(true);
        const files = await listFolderContents(formData.selectedFolder, workspace.account.id);
        setAssetFiles(files);
      } catch (error) {
        console.error('Error loading asset files:', error);
        setAssetFiles([]);
      } finally {
        setLoadingAssets(false);
      }
    };

    loadAssetFiles();
  }, [formData.selectedFolder, workspace.account.id, formData.sourceType]);

  // Convert seconds to minutes for display and check validation
  const lengthValidation = useMemo(() => {
    const seconds = parseInt(formData.length);
    if (isNaN(seconds) || seconds === 0) {
      return { display: "", isValid: false, error: "" };
    }

    if (seconds > 180) {
      return {
        display: `${Math.floor(seconds / 60)}:${(seconds % 60).toString().padStart(2, '0')} minutes`,
        isValid: false,
        error: "Video length cannot exceed 3 minutes (180 seconds)"
      };
    }

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    let display = "";
    if (minutes === 0) {
      display = `${remainingSeconds} seconds`;
    } else if (remainingSeconds === 0) {
      display = `${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`;
    } else {
      display = `${minutes}:${remainingSeconds.toString().padStart(2, '0')} minutes`;
    }

    return { display, isValid: true, error: "" };
  }, [formData.length]);

  const handleTypeChange = useCallback((value: VideoType) => {
    setFormData(prev => ({ ...prev, type: value }));
  }, []);

  const handleLengthChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numeric input
    if (value === "" || /^\d+$/.test(value)) {
      setFormData(prev => ({ ...prev, length: value }));
    }
  }, []);

  const handleSourceTypeChange = useCallback((value: SourceType) => {
    setFormData(prev => ({
      ...prev,
      sourceType: value,
      // Reset other source-related fields when changing source type
      selectedDocument: null,
      selectedFolder: null,
      selectedAsset: null,
      url: ""
      // Keep prompt field as it's not source-specific
    }));
  }, []);

  const handleUrlChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, url: e.target.value }));
  }, []);

  const handlePromptChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, prompt: e.target.value }));
  }, []);

  const handleAssetSelect = useCallback((asset: StorageFile) => {
    setFormData(prev => ({ ...prev, selectedAsset: asset }));
  }, []);

  const handleVideoAssetSelect = useCallback((asset: any) => {
    setFormData(prev => ({ ...prev, selectedVideoAsset: asset }));
  }, []);

  const handleBackToSetup = useCallback(() => {
    setCurrentStep('setup');
    setGenerationResult('');
    setIsGenerating(false);
  }, []);

  // Get file icon based on file type
  const getFileIcon = (file: StorageFile) => {
    if (!file.type) return <File className="w-4 h-4" />;

    if (file.type.startsWith('video/')) return <Video className="w-4 h-4" />;
    if (file.type.startsWith('image/')) return <Image className="w-4 h-4" />;
    if (file.type.startsWith('audio/')) return <Music className="w-4 h-4" />;
    if (file.type.includes('pdf') || file.type.includes('document')) return <FileText className="w-4 h-4" />;

    return <File className="w-4 h-4" />;
  };

  // Get file size from the file path or name
  const getFileName = (file: StorageFile) => {
    return file.name || file.path.split('/').pop() || 'Unknown file';
  };

  const handleGenerate = useCallback(async () => {
    try {
      setIsGenerating(true);
      setCurrentStep('review');
      
      console.log("Generate clicked with data:", {
        type: formData.type,
        length: formData.length,
        sourceType: formData.sourceType,
        selectedFile: formData.selectedDocument || formData.selectedAsset,
        url: formData.url,
        prompt: formData.prompt,
        selectedVideoAsset: formData.selectedVideoAsset
      });

      // Handle Repurpose Video differently
      if (formData.type === "Repurpose Video") {
        if (!formData.selectedVideoAsset) {
          console.error("No video asset selected for repurposing");
          setIsGenerating(false);
          setCurrentStep('setup');
          return;
        }

        // Prepare the repurpose request
        const repurposeRequest = {
          messages: [
            {
              role: 'user' as const,
              content: `Please repurpose this video/audio content: ${formData.prompt || 'Create highlights and key moments from this content'}`
            }
          ],
          userId: workspace.user.id,
          companyId: workspace.account.id,
          conversationId: crypto.randomUUID(),
          assetId: formData.selectedVideoAsset.id,
          assetPath: formData.selectedVideoAsset.file_path,
          transcriptUrl: formData.selectedVideoAsset.transcript_url,
          transcriptPath: formData.selectedVideoAsset.transcript_path,
          userPrompt: formData.prompt || null,
          fileName: formData.selectedVideoAsset.file_name,
          fileType: formData.selectedVideoAsset.file_type,
          hasTranscript: formData.selectedVideoAsset.has_transcript,
          // Add scene and project context for direct database insertion
          sceneId: selectedSceneId,
          projectId: selectedProjectId
        };

        console.log("🚀 Sending repurpose request:", repurposeRequest);
        console.log("🎬 Frontend context check:", {
          selectedSceneId,
          selectedProjectId,
          userCache,
          userId: workspace.user?.id
        });

        // Call the repurpose API
        const response = await fetch('/api/ai/repurpose-chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(repurposeRequest),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Repurpose API error:', errorData);
          setGenerationResult('❌ Error: Failed to process repurpose request. Please try again.');
          setIsGenerating(false);
          return;
        }

        const result = await response.json();
        console.log("✅ Repurpose result:", result);
        
        // Overlays are now inserted directly into the database by the backend
        // The frontend will automatically sync via Zero and display them
        console.log("🎬 Overlays were inserted directly into database - Zero sync will update timeline automatically");
        
        setGenerationResult(result.content || 'Analysis completed successfully!');
        setIsGenerating(false);
        return;
      }

      // Handle regular video generation
      // Determine the source URL based on source type
      let sourceUrl = '';
      if (formData.sourceType === "URL" && formData.url) {
        sourceUrl = formData.url;
      } else if (formData.sourceType === "Asset Library" && formData.selectedAsset) {
        // For asset library, we might need to handle differently
        // For now, log that we selected an asset
        console.log("Selected Asset:", formData.selectedAsset);
        setGenerationResult('🚧 Asset Library generation is not yet implemented. Please use URL source for now.');
        setIsGenerating(false);
        return;
      } else if (formData.sourceType === "Product Document" && formData.selectedDocument) {
        console.log("Selected Product Document:", formData.selectedDocument);
        setGenerationResult('🚧 Product Document generation is not yet implemented. Please use URL source for now.');
        setIsGenerating(false);
        return;
      }

      if (!sourceUrl) {
        console.error("No valid source URL found");
        setGenerationResult('❌ Error: No valid source URL found. Please provide a URL.');
        setIsGenerating(false);
        return;
      }

      // Prepare the generation request
      const generationRequest = {
        messages: [
          {
            role: 'user' as const,
            content: `Please generate video scenes for a ${formData.type} with the provided specifications.`
          }
        ],
        userId: workspace.user.id,
        companyId: workspace.account.id,
        conversationId: crypto.randomUUID(), // Generate unique conversation ID
        videoType: formData.type,
        videoDuration: parseInt(formData.length),
        sourceUrl: sourceUrl,
        userPrompt: formData.prompt || null,
        aspectRatio: '16:9' // Default aspect ratio
      };

      console.log("🚀 Sending generation request:", generationRequest);

      // Call the generation API
      const response = await fetch('/api/ai/generation-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(generationRequest),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Generation API error:', errorData);
        setGenerationResult('❌ Error: Failed to process generation request. Please try again.');
        setIsGenerating(false);
        return;
      }

      const result = await response.json();
      console.log("✅ Generation result:", result);
      setGenerationResult(result.content || 'Generation completed successfully!');
      setIsGenerating(false);

    } catch (error) {
      console.error("❌ Error during generation:", error);
      setGenerationResult('❌ Error: An unexpected error occurred. Please try again.');
      setIsGenerating(false);
      setCurrentStep('setup');
    }
  }, [formData, workspace]);

  // Check if form is valid for generation
  const isFormValid = useMemo(() => {
    const hasType = !!formData.type;
    
    // For repurpose video, we only need type and selected video asset
    if (formData.type === "Repurpose Video") {
      return hasType && !!formData.selectedVideoAsset;
    }
    
    // For other video types, we need length and source
    const hasValidLength = lengthValidation.isValid && formData.length.trim() !== "";
    const hasValidSource =
      (formData.sourceType === "Product Document" && formData.selectedDocument) ||
      (formData.sourceType === "Asset Library" && formData.selectedAsset) ||
      (formData.sourceType === "URL" && formData.url.trim() !== "");

    return hasType && hasValidLength && hasValidSource;
  }, [formData, lengthValidation.isValid]);

  const renderSourceSelection = () => {
    if (!formData.sourceType) return null;

    switch (formData.sourceType) {
      case "Product Document":
        return (
          <div className="space-y-3">
            <div className="text-sm text-muted-foreground">
              Product Documents will be displayed here
            </div>
            <div className="text-sm">
              <a
                href="#"
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
                onClick={(e) => {
                  e.preventDefault();
                  // TODO: Navigate to Product Information page
                  console.log("Navigate to Product Information page");
                }}
              >
                Add Product Documents in the Product Information page
              </a>
            </div>
            {/* TODO: Implement Product Documents list */}
          </div>
        );

      case "Asset Library":
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor="folder-select">Folder</Label>
              <Select onValueChange={(value) => setFormData(prev => ({ ...prev, selectedFolder: value, selectedAsset: null }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select folder" />
                </SelectTrigger>
                <SelectContent>
                  {assetFolders.map((folder) => (
                    <SelectItem key={folder.path} value={folder.path}>
                      {folder.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {formData.selectedFolder && (
              <div className="space-y-2">
                <Label>Assets</Label>
                {loadingAssets ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="ml-2 text-sm">Loading assets...</span>
                  </div>
                ) : assetFiles.length === 0 ? (
                  <div className="text-sm text-muted-foreground py-4 text-center">
                    No assets found in this folder
                  </div>
                ) : (
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {assetFiles.map((file) => (
                      <button
                        key={file.path}
                        onClick={() => handleAssetSelect(file)}
                        className={`w-full p-2 rounded border text-left transition-colors ${
                          formData.selectedAsset?.path === file.path
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                        }`}
                      >
                        <div className="flex items-center gap-2">
                          {getFileIcon(file)}
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm truncate">
                              {getFileName(file)}
                            </div>
                            {file.type && (
                              <div className="text-xs text-muted-foreground">
                                {file.type}
                              </div>
                            )}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}

            <div className="text-xs text-muted-foreground">
              To upload files, go to the Local tab or Asset Library
            </div>
          </div>
        );

      case "URL":
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor="url-input">URL</Label>
              <Input
                id="url-input"
                type="url"
                placeholder="https://example.com"
                value={formData.url}
                onChange={handleUrlChange}
                className="mt-1"
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col gap-6 p-4 bg-white dark:bg-gray-900/50 h-full">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Generate Video</h3>
          {currentStep === 'review' && (
            <Button variant="ghost" size="sm" onClick={handleBackToSetup}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Setup
            </Button>
          )}
        </div>

        {/* Breadcrumb Navigation */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink 
                onClick={() => currentStep === 'review' && handleBackToSetup()}
                className={currentStep === 'setup' ? 'cursor-default' : 'cursor-pointer'}
              >
                Setup
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              {currentStep === 'review' ? (
                <BreadcrumbPage>Review</BreadcrumbPage>
              ) : (
                <span className="text-muted-foreground">Review</span>
              )}
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <ScrollArea className="h-[calc(100vh-220px)]">
          <div className="space-y-6 pr-4">
            {currentStep === 'setup' ? (
              // Setup Form Content
              <>
            {/* Type Selection */}
            <div className="space-y-2">
             
            </div>
            {/* Type Selection */}
            <div className="space-y-2">
              <Label htmlFor="type-select">Type</Label>
              <Select onValueChange={handleTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select video type" />
                </SelectTrigger>
                <SelectContent>
                  {VIDEO_TYPES.map((type) => (
                    <SelectItem 
                      key={type.value} 
                      value={type.value}
                      disabled={type.disabled}
                      className={type.disabled ? "opacity-50 cursor-not-allowed" : ""}
                    >
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Length Input - Hidden for Repurpose Video */}
            {formData.type !== "Repurpose Video" && (
              <div className="space-y-2">
                <Label htmlFor="length-input">Length (seconds)</Label>
                <Input
                  id="length-input"
                  type="text"
                  placeholder="e.g., 30"
                  value={formData.length}
                  onChange={handleLengthChange}
                  className={`mt-1 ${
                    lengthValidation.error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                  }`}
                />
                {lengthValidation.display && (
                  <div className={`text-xs ${
                    lengthValidation.error ? 'text-red-500' : 'text-muted-foreground'
                  }`}>
                    {lengthValidation.display}
                  </div>
                )}
                {lengthValidation.error && (
                  <div className="text-xs text-red-500 font-medium">
                    {lengthValidation.error}
                  </div>
                )}
              </div>
            )}

            {/* Source Selection - Hidden for Repurpose Video */}
            {formData.type !== "Repurpose Video" && (
              <div className="space-y-2">
                <Label htmlFor="source-select">Source</Label>
                <Select onValueChange={handleSourceTypeChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select source type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Product Document">Product Document</SelectItem>
                    <SelectItem value="Asset Library">Asset Library</SelectItem>
                    <SelectItem value="URL">URL</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Dynamic Source Content or Repurpose Video Selection */}
            {formData.type === "Repurpose Video" ? (
              <div className="space-y-4">
                {/* Video/Audio Asset Selection */}
                <div className="space-y-2">
                  <Label>Select Video or Audio File</Label>
                  <div className="text-sm text-muted-foreground mb-3">
                    Choose a video or audio file from your uploaded assets to repurpose
                  </div>
                  
                  {!videoAudioAssets || videoAudioAssets.length === 0 ? (
                    <div className="text-sm text-muted-foreground py-4 text-center border rounded">
                      No video or audio files found. Upload files to the Asset Library first.
                    </div>
                  ) : (
                    <div className="space-y-2 max-h-60 overflow-y-auto border rounded p-3">
                      {videoAudioAssets.map((asset) => (
                        <button
                          key={asset.id}
                          onClick={() => handleVideoAssetSelect(asset)}
                          className={`w-full p-3 rounded border text-left transition-colors ${
                            formData.selectedVideoAsset?.id === asset.id
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                          }`}
                        >
                          <div className="flex items-center gap-3">
                            {asset.file_type?.startsWith('video/') ? (
                              <Video className="w-5 h-5 text-blue-600" />
                            ) : (
                              <Music className="w-5 h-5 text-green-600" />
                            )}
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-sm truncate">
                                {asset.file_name}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {asset.file_type} • {asset.folder_name}
                                {asset.has_transcript && (
                                  <span className="ml-2 text-green-600">✓ Has transcript</span>
                                )}
                              </div>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Repurpose Prompt */}
                <div className="space-y-2">
                  <Label htmlFor="repurpose-prompt">Repurpose Instructions</Label>
                  <Textarea
                    id="repurpose-prompt"
                    placeholder="Describe how you want to repurpose this content (e.g., 'Create 3 short clips for social media', 'Extract key highlights', 'Create a 30-second teaser')..."
                    value={formData.prompt}
                    onChange={handlePromptChange}
                    className="min-h-[80px] resize-none"
                  />
                  <div className="text-xs text-muted-foreground">
                    Specify how you want to repurpose the selected video/audio content
                  </div>
                </div>
              </div>
            ) : (
              <>
                {/* Dynamic Source Content */}
                {renderSourceSelection()}

                {/* Prompt Input (Optional) */}
                <div className="space-y-2">
                  <Label htmlFor="prompt-input">Prompt (Optional)</Label>
                  <Textarea
                    id="prompt-input"
                    placeholder="Describe any specific requirements or style for your video..."
                    value={formData.prompt}
                    onChange={handlePromptChange}
                    className="min-h-[80px] resize-none"
                  />
                  <div className="text-xs text-muted-foreground">
                    Add any specific instructions or requirements for your video generation
                  </div>
                </div>
              </>
            )}

            {/* Generate Button */}
            <div className="pt-4">
              <Button
                onClick={handleGenerate}
                disabled={!isFormValid}
                className="w-full"
                size="lg"
              >
                Generate Video
              </Button>
            </div>
              </>
            ) : (
              // Review Section
              <div className="space-y-6">
                <div className="text-center">
                  <h4 className="text-xl font-semibold mb-2">Generation Results</h4>
                  <p className="text-muted-foreground">
                    {formData.type === "Repurpose Video" 
                      ? "Review your video repurpose analysis below"
                      : "Review your video generation results below"
                    }
                  </p>
                </div>

                {isGenerating ? (
                  <div className="flex flex-col items-center justify-center py-12 space-y-4">
                    <Loader2 className="w-8 h-8 animate-spin" />
                    <p className="text-muted-foreground">
                      {formData.type === "Repurpose Video" 
                        ? "Analyzing transcript and extracting clips..."
                        : "Generating video content..."
                      }
                    </p>
                  </div>
                ) : (
                  <div className="bg-muted/50 rounded-lg p-6">
                    <div className="prose prose-sm max-w-none dark:prose-invert">
                      <div dangerouslySetInnerHTML={{ 
                        __html: generationResult.replace(/\n/g, '<br />').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>').replace(/## (.*?)(\n|$)/g, '<h3>$1</h3>').replace(/### (.*?)(\n|$)/g, '<h4>$1</h4>')
                      }} />
                    </div>
                  </div>
                )}

                <div className="flex gap-3 pt-4">
                  <Button variant="outline" onClick={handleBackToSetup} className="flex-1">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Setup
                  </Button>
                  {!isGenerating && generationResult && formData.type === "Repurpose Video" && (
                    <Button className="flex-1" variant="default" disabled>
                      ✅ Added to Timeline
                    </Button>
                  )}
                  {!isGenerating && generationResult && formData.type !== "Repurpose Video" && (
                    <Button className="flex-1">
                      Add to Timeline
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};