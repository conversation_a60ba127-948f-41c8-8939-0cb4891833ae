import React from "react";
import { registerRoot, Composition } from "remotion";
import { Main } from "./main";
import { FPS, DURATION_IN_FRAMES, VIDEO_WIDTH, VIDEO_HEIGHT } from "../constants";

// Lambda-specific root component
const LambdaRoot: React.FC = () => {
  const defaultProps = {
    overlays: [],
    durationInFrames: DURATION_IN_FRAMES,
    fps: FPS,
    width: VIDEO_WIDTH,
    height: VIDEO_HEIGHT,
    src: "",
    setSelectedOverlayId: () => {},
    selectedOverlayId: null as string | null,
    changeOverlay: () => {},
    isLambdaRender: true,
    baseUrl: "", // Add baseUrl for asset resolution
  };

  return (
    <>
      <Composition
        id="lambda-video"
        component={Main}
        durationInFrames={DURATION_IN_FRAMES}
        fps={FPS}
        width={VIDEO_WIDTH}
        height={VIDEO_HEIGHT}
        calculateMetadata={async ({ props }) => {
          // Use dynamic values from inputProps, with fallbacks to defaults
          return {
            durationInFrames: props.durationInFrames || DURATION_IN_FRAMES,
            width: props.width || VIDEO_WIDTH,
            height: props.height || VIDEO_HEIGHT,
          };
        }}
        defaultProps={defaultProps}
      />
    </>
  );
};

registerRoot(LambdaRoot);
