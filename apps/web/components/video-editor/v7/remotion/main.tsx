import React, { useCallback } from "react";
import { AbsoluteFill } from "remotion";

import { Overlay } from "../types";
import { SortedOutlines } from "../components/selection/sorted-outlines";
import { Layer } from "../components/core/layer";
import { LambdaDebug } from "../components/debug/lambda-debug";

/**
 * Props for the Main component
 */
export type MainProps = {
  /** Array of overlay objects to be rendered */
  readonly overlays: Overlay[];
  /** Function to set the currently selected overlay ID */
  readonly setSelectedOverlayId: React.Dispatch<
    React.SetStateAction<string | null>
  >;
  /** Currently selected overlay ID, or null if none selected */
  readonly selectedOverlayId: string | null;
  /**
   * Function to update an overlay
   * @param overlayId - The ID of the overlay to update
   * @param updater - Function that receives the current overlay and returns an updated version
   */
  readonly changeOverlay: (
    overlayId: string,
    updater: (overlay: Overlay) => Overlay
  ) => void;
  /** Duration in frames of the composition */
  readonly durationInFrames: number;
  /** Frames per second of the composition */
  readonly fps: number;
  /** Width of the composition */
  readonly width: number;
  /** Height of the composition */
  readonly height: number;
  /** Base URL for media assets (optional) */
  readonly baseUrl?: string;
  /** Whether this is being rendered on Lambda (no interactive elements) */
  readonly isLambdaRender?: boolean;
};

const outer: React.CSSProperties = {
  backgroundColor: "#111827",
};

const layerContainer: React.CSSProperties = {
  overflow: "hidden",
  maxWidth: "3000px",
};

/**
 * Main component that renders a canvas-like area with overlays and their outlines.
 * Handles selection of overlays and provides a container for editing them.
 *
 * @param props - Component props of type MainProps
 * @returns React component that displays overlays and their interactive outlines
 */
export const Main: React.FC<MainProps> = ({
  overlays,
  setSelectedOverlayId,
  selectedOverlayId,
  changeOverlay,
  baseUrl,
  isLambdaRender = false,
}) => {
  console.log(`🎬 [MAIN] Rendering with ${overlays?.length || 0} overlays, isLambdaRender: ${isLambdaRender}, baseUrl: ${baseUrl}`);

  // Log overlay details for debugging
  if (overlays && overlays.length > 0) {
    overlays.forEach((overlay, index) => {
      console.log(`🎬 [MAIN] Overlay ${index}:`, {
        id: overlay.id,
        type: overlay.type,
        src: overlay.src,
        from: overlay.from,
        durationInFrames: overlay.durationInFrames,
        width: overlay.width,
        height: overlay.height,
      });
    });
  } else {
    console.warn(`🎬 [MAIN] No overlays provided to render!`);
  }

  const onPointerDown = useCallback(
    (e: React.PointerEvent) => {
      // Skip pointer events for Lambda rendering
      if (isLambdaRender || e.button !== 0) {
        return;
      }

      setSelectedOverlayId(null);
    },
    [setSelectedOverlayId, isLambdaRender]
  );

  return (
    <AbsoluteFill
      style={{
        ...outer,
      }}
      onPointerDown={onPointerDown}
    >
      <AbsoluteFill style={layerContainer}>
        {overlays.map((overlay) => {
          return (
            <Layer
              key={overlay.id}
              overlay={overlay}
              selectedOverlayId={isLambdaRender ? null : selectedOverlayId}
              baseUrl={baseUrl}
              isLambdaRender={isLambdaRender}
            />
          );
        })}
      </AbsoluteFill>
      {/* Only render interactive elements for non-Lambda rendering */}
      {!isLambdaRender && (
        <SortedOutlines
          selectedOverlayId={selectedOverlayId}
          overlays={overlays}
          setSelectedOverlayId={setSelectedOverlayId}
          changeOverlay={changeOverlay}
        />
      )}
      {/* Debug overlay for Lambda rendering - remove in production */}
      {isLambdaRender && process.env.NODE_ENV === 'development' && (
        <LambdaDebug
          overlays={overlays}
          baseUrl={baseUrl}
          isLambdaRender={isLambdaRender}
          durationInFrames={overlays.reduce((max, overlay) =>
            Math.max(max, overlay.from + overlay.durationInFrames), 0)}
          width={1280}
          height={720}
        />
      )}
    </AbsoluteFill>
  );
};
