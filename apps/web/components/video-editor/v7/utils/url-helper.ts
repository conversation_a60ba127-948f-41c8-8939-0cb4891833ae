/**
 * URL Helper Utility
 *
 * Provides functions to handle URL transformations for consistent
 * access between browser and SSR renderer.
 */

/**
 * Get the base URL from environment variables or default to localhost:3000
 */
export const getBaseUrl = (): string => {
  // Use environment variable if available, otherwise default to localhost:3000
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
  console.log(`🔗 [URL-HELPER] getBaseUrl returning: ${baseUrl}`);
  return baseUrl;
};

/**
 * Convert a relative URL to an absolute URL
 *
 * @param url The URL to convert
 * @returns Absolute URL with the correct base
 */
export const toAbsoluteUrl = (url: string): string => {
  console.log(`🔗 [URL-HELPER] toAbsoluteUrl input: ${url}`);

  // If the URL is already absolute, return it as is
  if (url.startsWith("http://") || url.startsWith("https://")) {
    console.log(`🔗 [URL-HELPER] Already absolute: ${url}`);
    return url;
  }

  // If it's a relative URL starting with /, add the base URL
  if (url.startsWith("/")) {
    const result = `${getBaseUrl()}${url}`;
    console.log(`🔗 [URL-HELPER] Relative with /: ${result}`);
    return result;
  }

  // Otherwise, add the base URL with a / separator
  const result = `${getBaseUrl()}/${url}`;
  console.log(`🔗 [URL-HELPER] Relative without /: ${result}`);
  return result;
};

/**
 * Resolves a media URL to ensure it works in both browser and SSR contexts
 *
 * @param url The URL to resolve
 * @param baseUrl Optional base URL to use
 * @returns Properly formatted URL for the current context
 */
export const resolveMediaUrl = (url: string, baseUrl?: string): string => {
  // If the URL is already absolute, return it as is
  if (url.startsWith("http://") || url.startsWith("https://")) {
    return url;
  }

  // If baseUrl is provided, use it
  if (baseUrl) {
    // Ensure there's no double slash
    if (url.startsWith("/") && baseUrl.endsWith("/")) {
      return `${baseUrl}${url.substring(1)}`;
    }

    // Ensure there's at least one slash
    if (!url.startsWith("/") && !baseUrl.endsWith("/")) {
      return `${baseUrl}/${url}`;
    }

    return `${baseUrl}${url}`;
  }

  // If we're in the browser, use window.location.origin
  if (typeof window !== "undefined") {
    return `${window.location.origin}${url.startsWith("/") ? "" : "/"}${url}`;
  }

  // In SSR context, use the base URL
  return toAbsoluteUrl(url);
};
