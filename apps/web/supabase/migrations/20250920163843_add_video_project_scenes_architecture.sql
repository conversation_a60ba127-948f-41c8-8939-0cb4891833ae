-- Video Editor Scenes Architecture Migration
-- This migration creates the scene-based structure for video projects
-- Since all existing records were deleted, we can create a clean schema

-- Step 1: Create video_project_scenes table
CREATE TABLE IF NOT EXISTS public.video_project_scenes (
    id uuid PRIMARY KEY,
    project_id uuid NOT NULL REFERENCES public.video_projects(id) ON DELETE CASCADE,
    name text NOT NULL,
    description text,
    order_index integer DEFAULT 1,
    duration_frames integer,
    aspect_ratio text,
    created_at bigint,
    updated_at bigint,
    user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Step 2: Create video_project_scene_selections table
-- This tracks the last selected scene per user per project
CREATE TABLE IF NOT EXISTS public.video_project_scene_selections (
    id uuid PRIMARY KEY,
    project_id uuid NOT NULL REFERENCES public.video_projects(id) ON DELETE CASCADE,
    user_id uuid NOT NULL,
    selected_scene_id uuid NOT NULL REFERENCES public.video_project_scenes(id) ON DELETE CASCADE,
    updated_at bigint,
    UNIQUE(project_id, user_id)
);

-- Step 3: Update video_project_overlays to add scene_id (keeping project_id)
ALTER TABLE public.video_project_overlays 
ADD COLUMN IF NOT EXISTS scene_id uuid;

-- Since there are no existing records, we can make it NOT NULL immediately
ALTER TABLE public.video_project_overlays 
ALTER COLUMN scene_id SET NOT NULL;

-- Add foreign key constraint for scene_id
ALTER TABLE public.video_project_overlays 
ADD CONSTRAINT fk_video_project_overlays_scene_id 
FOREIGN KEY (scene_id) REFERENCES public.video_project_scenes(id) ON DELETE CASCADE;

-- Keep the existing project_id column and its foreign key constraint

-- Step 4: Update video_project_autosaves to add scene_id (keeping project_id)
ALTER TABLE public.video_project_autosaves 
ADD COLUMN IF NOT EXISTS scene_id uuid;

-- Make it NOT NULL immediately
ALTER TABLE public.video_project_autosaves 
ALTER COLUMN scene_id SET NOT NULL;

-- Add foreign key constraint for scene_id
ALTER TABLE public.video_project_autosaves 
ADD CONSTRAINT fk_video_project_autosaves_scene_id 
FOREIGN KEY (scene_id) REFERENCES public.video_project_scenes(id) ON DELETE CASCADE;

-- Keep the existing project_id column and its foreign key constraint

-- Step 5: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_video_project_scenes_project_id 
ON public.video_project_scenes(project_id);

CREATE INDEX IF NOT EXISTS idx_video_project_scenes_order_index 
ON public.video_project_scenes(project_id, order_index);

CREATE INDEX IF NOT EXISTS idx_video_project_scene_selections_project_user 
ON public.video_project_scene_selections(project_id, user_id);

CREATE INDEX IF NOT EXISTS idx_video_project_overlays_scene_id 
ON public.video_project_overlays(scene_id);

CREATE INDEX IF NOT EXISTS idx_video_project_autosaves_scene_id 
ON public.video_project_autosaves(scene_id);

-- Step 6: Enable Row Level Security (RLS) on new tables
ALTER TABLE public.video_project_scenes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.video_project_scene_selections ENABLE ROW LEVEL SECURITY;

-- Step 7: Create RLS policies for video_project_scenes
-- Users can view scenes for projects they have access to
CREATE POLICY "Users can view project scenes" 
ON public.video_project_scenes FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM public.video_projects vp
        JOIN public.accounts_memberships am ON am.account_id = vp.account_id
        WHERE vp.id = video_project_scenes.project_id 
        AND am.user_id = auth.uid()
    )
);

-- Users can insert scenes for projects they have access to
CREATE POLICY "Users can insert project scenes" 
ON public.video_project_scenes FOR INSERT 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.video_projects vp
        JOIN public.accounts_memberships am ON am.account_id = vp.account_id
        WHERE vp.id = video_project_scenes.project_id 
        AND am.user_id = auth.uid()
    )
);

-- Users can update scenes for projects they have access to
CREATE POLICY "Users can update project scenes" 
ON public.video_project_scenes FOR UPDATE 
USING (
    EXISTS (
        SELECT 1 FROM public.video_projects vp
        JOIN public.accounts_memberships am ON am.account_id = vp.account_id
        WHERE vp.id = video_project_scenes.project_id 
        AND am.user_id = auth.uid()
    )
);

-- Users can delete scenes for projects they have access to
CREATE POLICY "Users can delete project scenes" 
ON public.video_project_scenes FOR DELETE 
USING (
    EXISTS (
        SELECT 1 FROM public.video_projects vp
        JOIN public.accounts_memberships am ON am.account_id = vp.account_id
        WHERE vp.id = video_project_scenes.project_id 
        AND am.user_id = auth.uid()
    )
);

-- Step 8: Create RLS policies for video_project_scene_selections
-- Users can only access their own scene selections
CREATE POLICY "Users can manage their own scene selections" 
ON public.video_project_scene_selections FOR ALL 
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- Step 9: Update existing RLS policies for video_project_overlays
-- Drop existing policies that reference project_id
DROP POLICY IF EXISTS "Users can view project overlays" ON public.video_project_overlays;
DROP POLICY IF EXISTS "Users can insert project overlays" ON public.video_project_overlays;
DROP POLICY IF EXISTS "Users can update project overlays" ON public.video_project_overlays;
DROP POLICY IF EXISTS "Users can delete project overlays" ON public.video_project_overlays;

-- Create new policies that reference scene_id
CREATE POLICY "Users can view scene overlays" 
ON public.video_project_overlays FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM public.video_project_scenes vps
        JOIN public.video_projects vp ON vp.id = vps.project_id
        JOIN public.accounts_memberships am ON am.account_id = vp.account_id
        WHERE vps.id = video_project_overlays.scene_id 
        AND am.user_id = auth.uid()
    )
);

CREATE POLICY "Users can insert scene overlays" 
ON public.video_project_overlays FOR INSERT 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.video_project_scenes vps
        JOIN public.video_projects vp ON vp.id = vps.project_id
        JOIN public.accounts_memberships am ON am.account_id = vp.account_id
        WHERE vps.id = video_project_overlays.scene_id 
        AND am.user_id = auth.uid()
    )
);

CREATE POLICY "Users can update scene overlays" 
ON public.video_project_overlays FOR UPDATE 
USING (
    EXISTS (
        SELECT 1 FROM public.video_project_scenes vps
        JOIN public.video_projects vp ON vp.id = vps.project_id
        JOIN public.accounts_memberships am ON am.account_id = vp.account_id
        WHERE vps.id = video_project_overlays.scene_id 
        AND am.user_id = auth.uid()
    )
);

CREATE POLICY "Users can delete scene overlays" 
ON public.video_project_overlays FOR DELETE 
USING (
    EXISTS (
        SELECT 1 FROM public.video_project_scenes vps
        JOIN public.video_projects vp ON vp.id = vps.project_id
        JOIN public.accounts_memberships am ON am.account_id = vp.account_id
        WHERE vps.id = video_project_overlays.scene_id 
        AND am.user_id = auth.uid()
    )
);

-- Step 10: Update RLS policies for video_project_autosaves
-- Drop existing policies
DROP POLICY IF EXISTS "Users can view project autosaves" ON public.video_project_autosaves;
DROP POLICY IF EXISTS "Users can insert project autosaves" ON public.video_project_autosaves;
DROP POLICY IF EXISTS "Users can delete project autosaves" ON public.video_project_autosaves;

-- Create new policies for scene-based autosaves
CREATE POLICY "Users can view scene autosaves" 
ON public.video_project_autosaves FOR SELECT 
USING (user_id = auth.uid());

CREATE POLICY "Users can insert scene autosaves" 
ON public.video_project_autosaves FOR INSERT 
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete scene autosaves" 
ON public.video_project_autosaves FOR DELETE 
USING (user_id = auth.uid());

-- Step 11: Add selected_scene_id to user_cache for tracking current scene
ALTER TABLE public.user_cache 
ADD COLUMN IF NOT EXISTS selected_scene_id uuid REFERENCES public.video_project_scenes(id) ON DELETE SET NULL;

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_user_cache_selected_scene_id 
ON public.user_cache(selected_scene_id) 
WHERE selected_scene_id IS NOT NULL;

-- Step 12: Add helpful comments
COMMENT ON TABLE public.video_project_scenes IS 'Individual scenes within video projects. Each scene contains its own set of overlays.';
COMMENT ON TABLE public.video_project_scene_selections IS 'Tracks the last selected scene per user per project for better UX.';
COMMENT ON COLUMN public.video_project_overlays.scene_id IS 'References the scene this overlay belongs to (added alongside existing project_id).';
COMMENT ON COLUMN public.video_project_autosaves.scene_id IS 'References the scene this autosave belongs to (added alongside existing project_id).';
COMMENT ON COLUMN public.user_cache.selected_scene_id IS 'Tracks the currently selected scene per user for better UX.';

-- Migration completed successfully!
-- 
-- Summary of changes:
-- 1. Created video_project_scenes table
-- 2. Created video_project_scene_selections table  
-- 3. Added scene_id to video_project_overlays (keeping existing project_id)
-- 4. Added scene_id to video_project_autosaves (keeping existing project_id)
-- 5. Added selected_scene_id to user_cache for tracking current scene
-- 6. Created appropriate indexes and RLS policies
-- 7. Both project_id and scene_id are now available for overlays and autosaves
--
-- Next steps:
-- 1. Deploy your updated Zero schema
-- 2. Create your first project - it will need at least one scene
-- 3. All new overlays and autosaves will be scene-based
