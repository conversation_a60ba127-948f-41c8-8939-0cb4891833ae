-- Migration: Update video_project_overlays field names to match frontend
-- Date: 2025-01-23
-- Description: Rename database columns to match Zero schema and frontend variable names
-- This eliminates the need for conversion functions between frontend and database

-- Step 1: Remove the overlay_id column since we're using id as primary key directly
ALTER TABLE public.video_project_overlays 
DROP COLUMN IF EXISTS overlay_id;

-- Step 2: Rename overlay_type to type
ALTER TABLE public.video_project_overlays 
RENAME COLUMN overlay_type TO type;

-- Step 3: Rename duration_frames to durationInFrames  
ALTER TABLE public.video_project_overlays 
RENAME COLUMN duration_frames TO "durationInFrames";

-- Step 4: Rename from_frame to from
ALTER TABLE public.video_project_overlays 
RENAME COLUMN from_frame TO "from";

-- Step 5: Rename row_position to row
ALTER TABLE public.video_project_overlays 
RENAME COLUMN row_position TO "row";

-- Step 6: Rename position_x to left
ALTER TABLE public.video_project_overlays 
RENAME COLUMN position_x TO "left";

-- Step 7: Rename position_y to top  
ALTER TABLE public.video_project_overlays 
RENAME COLUMN position_y TO "top";

-- Step 8: Add isDragging column (boolean, defaults to false)
ALTER TABLE public.video_project_overlays 
ADD COLUMN IF NOT EXISTS "isDragging" BOOLEAN DEFAULT FALSE;

-- Step 9: Add loading column (boolean, defaults to false)
ALTER TABLE public.video_project_overlays 
ADD COLUMN IF NOT EXISTS loading BOOLEAN DEFAULT FALSE;

-- Step 10: Add overlay-specific fields that were missing
ALTER TABLE public.video_project_overlays 
ADD COLUMN IF NOT EXISTS transcript TEXT,
ADD COLUMN IF NOT EXISTS "videoStartTime" DECIMAL(10,4),
ADD COLUMN IF NOT EXISTS "startFromSound" DECIMAL(10,4),
ADD COLUMN IF NOT EXISTS speed DECIMAL(6,2),
ADD COLUMN IF NOT EXISTS duration DECIMAL(10,4),
ADD COLUMN IF NOT EXISTS captions JSONB,
ADD COLUMN IF NOT EXISTS template TEXT,
ADD COLUMN IF NOT EXISTS category TEXT;

-- Step 13: Remove unused columns that are not in the frontend schema
-- Remove layer_order as it's not used in the current frontend implementation
ALTER TABLE public.video_project_overlays 
DROP COLUMN IF EXISTS layer_order;

-- Remove animation_config as it's not in the current Zero schema
ALTER TABLE public.video_project_overlays 
DROP COLUMN IF EXISTS animation_config;

-- Keep metadata column as it exists in Zero schema

-- Step 14: Update indexes to use new column names
DROP INDEX IF EXISTS idx_video_project_overlays_layer_order;

-- Create new indexes for performance
CREATE INDEX IF NOT EXISTS idx_video_project_overlays_scene_id 
  ON public.video_project_overlays(scene_id);

CREATE INDEX IF NOT EXISTS idx_video_project_overlays_from 
  ON public.video_project_overlays(scene_id, "from");

CREATE INDEX IF NOT EXISTS idx_video_project_overlays_type 
  ON public.video_project_overlays(scene_id, type);

-- Step 15: Add comments to document the new schema
COMMENT ON TABLE public.video_project_overlays IS 'Video overlay data with field names matching frontend Zero schema';
COMMENT ON COLUMN public.video_project_overlays.type IS 'Overlay type: text, image, video, sound, caption, sticker';
COMMENT ON COLUMN public.video_project_overlays."durationInFrames" IS 'Duration of overlay in frames';
COMMENT ON COLUMN public.video_project_overlays."from" IS 'Starting frame position';
COMMENT ON COLUMN public.video_project_overlays."row" IS 'Timeline row/track number';
COMMENT ON COLUMN public.video_project_overlays."left" IS 'X position in composition';
COMMENT ON COLUMN public.video_project_overlays."top" IS 'Y position in composition';
COMMENT ON COLUMN public.video_project_overlays."isDragging" IS 'UI state for drag operations';
COMMENT ON COLUMN public.video_project_overlays.loading IS 'UI state for loading operations';
COMMENT ON COLUMN public.video_project_overlays.transcript IS 'Transcript text for video/audio overlays';
COMMENT ON COLUMN public.video_project_overlays."videoStartTime" IS 'Start time offset for video overlays';
COMMENT ON COLUMN public.video_project_overlays."startFromSound" IS 'Start time offset for audio overlays';
COMMENT ON COLUMN public.video_project_overlays.speed IS 'Playback speed multiplier for media overlays';
COMMENT ON COLUMN public.video_project_overlays.duration IS 'Duration in seconds for media overlays';
COMMENT ON COLUMN public.video_project_overlays.captions IS 'Caption data for caption overlays';
COMMENT ON COLUMN public.video_project_overlays.template IS 'Template identifier for caption overlays';
COMMENT ON COLUMN public.video_project_overlays.category IS 'Category for sticker overlays';
