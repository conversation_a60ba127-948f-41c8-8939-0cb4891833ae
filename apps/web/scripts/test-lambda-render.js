#!/usr/bin/env node

/**
 * Test script to verify Lambda rendering with proper asset URLs
 * Run with: node scripts/test-lambda-render.js
 */

const { renderMediaOnLambda, getRenderProgress } = require('@remotion/lambda/client');

const LAMBDA_CONFIG = {
  FUNCTION_NAME: 'remotion-render-4-0-272-mem2048mb-disk2048mb-900sec',
  REGION: 'us-east-1',
  SITE_NAME: 'axcels-video-studio',
  FRAMES_PER_LAMBDA: 100,
  MAX_RETRIES: 2,
  CODEC: 'h264',
};

// Test input props with sample overlays
const testInputProps = {
  overlays: [
    {
      id: 'test-text-overlay',
      type: 'text',
      content: 'Test Text Overlay',
      left: 100,
      top: 100,
      width: 400,
      height: 100,
      from: 0,
      durationInFrames: 90,
      row: 0,
      rotation: 0,
      isDragging: false,
      styles: {
        fontSize: '2rem',
        fontWeight: '700',
        color: 'rgba(255, 255, 255, 1)',
        textAlign: 'center',
      },
    },
    {
      id: 'test-video-overlay',
      type: 'video',
      content: 'Test Video',
      src: '/api/storage/sample-video.mp4', // This should be a real video URL
      left: 0,
      top: 200,
      width: 640,
      height: 360,
      from: 30,
      durationInFrames: 60,
      row: 1,
      rotation: 0,
      isDragging: false,
      videoStartTime: 0,
      duration: 2,
      styles: {
        objectFit: 'cover',
        volume: 1,
        opacity: 1,
      },
    },
  ],
  durationInFrames: 90,
  width: 1280,
  height: 720,
  fps: 30,
  src: '',
  baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
};

async function testLambdaRender() {
  console.log('🧪 [TEST] Starting Lambda render test...');
  console.log('🧪 [TEST] Input props:', JSON.stringify(testInputProps, null, 2));

  try {
    // Start the render
    console.log('🧪 [TEST] Initiating render...');
    const { renderId, bucketName } = await renderMediaOnLambda({
      codec: LAMBDA_CONFIG.CODEC,
      functionName: LAMBDA_CONFIG.FUNCTION_NAME,
      region: LAMBDA_CONFIG.REGION,
      serveUrl: LAMBDA_CONFIG.SITE_NAME,
      composition: 'lambda-video',
      inputProps: testInputProps,
      framesPerLambda: LAMBDA_CONFIG.FRAMES_PER_LAMBDA,
      downloadBehavior: {
        type: 'download',
        fileName: 'test-video.mp4',
      },
      maxRetries: LAMBDA_CONFIG.MAX_RETRIES,
      everyNthFrame: 1,
    });

    console.log(`🧪 [TEST] Render started! renderId: ${renderId}, bucketName: ${bucketName}`);

    // Poll for progress
    let pending = true;
    while (pending) {
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds

      const progress = await getRenderProgress({
        renderId,
        bucketName,
        functionName: LAMBDA_CONFIG.FUNCTION_NAME,
        region: LAMBDA_CONFIG.REGION,
      });

      console.log(`🧪 [TEST] Progress: ${Math.round(progress.overallProgress * 100)}%`);

      if (progress.done) {
        console.log('🧪 [TEST] ✅ Render complete!');
        console.log('🧪 [TEST] Output URL:', progress.outputFile);
        console.log('🧪 [TEST] File size:', progress.outputSizeInBytes, 'bytes');
        pending = false;
      }

      if (progress.fatalErrorEncountered) {
        console.error('🧪 [TEST] ❌ Fatal error encountered:');
        console.error(progress.errors);
        pending = false;
      }
    }
  } catch (error) {
    console.error('🧪 [TEST] ❌ Test failed:', error);
  }
}

// Run the test
testLambdaRender().catch(console.error);
