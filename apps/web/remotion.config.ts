import { Config } from '@remotion/cli/config';
import path from 'path';

Config.setVideoImageFormat('jpeg');
Config.setOverwriteOutput(true);
Config.setPixelFormat('yuv420p');
Config.setCodec('h264');

// Configure webpack to handle path resolution and mock UI components
Config.overrideWebpackConfig((config) => {
  return {
    ...config,
    resolve: {
      ...config.resolve,
      alias: {
        ...config.resolve?.alias,
        // Map Next.js aliases to actual paths
        '@': path.resolve(__dirname),
        '@/components': path.resolve(__dirname, 'components'),
        // Mock UI components for Lambda rendering
        '@/components/ui/tabs': path.resolve(__dirname, 'components/video-editor/v7/remotion/mock-ui.tsx'),
      },
    },
  };
});
