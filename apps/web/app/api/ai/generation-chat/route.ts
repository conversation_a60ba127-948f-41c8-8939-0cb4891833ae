import { NextRequest } from 'next/server';

export const runtime = 'edge';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

interface RequestBody {
  messages: ChatMessage[];
  userId: string;
  companyId: string;
  conversationId: string;
  messageId?: string;
  // Video generation specific parameters
  videoType?: string;
  videoDuration?: number;
  sourceUrl?: string;
  userPrompt?: string;
  aspectRatio?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: RequestBody = await request.json();
    const { 
      messages, 
      userId, 
      companyId, 
      conversationId, 
      messageId,
      videoType,
      videoDuration,
      sourceUrl,
      userPrompt,
      aspectRatio
    } = body;

    console.log('📡 [API-ROUTE] Received generation parameters:', { 
      videoType, 
      videoDuration, 
      sourceUrl, 
      userPrompt, 
      aspectRatio, 
      userId, 
      companyId 
    });

    // Validate required fields
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return new Response(
        JSON.stringify({ error: 'Messages array is required and cannot be empty' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    if (!userId || !companyId || !conversationId) {
      return new Response(
        JSON.stringify({ error: 'userId, companyId, and conversationId are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Forward request to sb-server
    const sbServerUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || 'http://localhost:8080';
    const response = await fetch(`${sbServerUrl}/generation-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages,
        userId,
        companyId,
        conversationId,
        messageId,
        videoType,
        videoDuration,
        sourceUrl,
        userPrompt,
        aspectRatio
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error from sb-server:', errorText);
      return new Response(
        JSON.stringify({ error: 'Failed to get response from AI service' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Parse and return the JSON response
    const data = await response.json();
    return Response.json(data);

  } catch (error) {
    console.error('Error in generation chat API:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
