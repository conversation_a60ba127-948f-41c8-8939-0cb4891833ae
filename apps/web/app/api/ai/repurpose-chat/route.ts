import { NextRequest } from 'next/server';

export const runtime = 'edge';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

interface RequestBody {
  messages: ChatMessage[];
  userId: string;
  companyId: string;
  conversationId: string;
  messageId?: string;
  // Repurpose-specific parameters
  assetId: string;
  assetPath: string;
  transcriptUrl?: string;
  transcriptPath?: string;
  userPrompt: string;
  fileName: string;
  fileType: string;
  hasTranscript?: boolean;
  // Database context for direct overlay insertion
  sceneId?: string;
  projectId?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: RequestBody = await request.json();
    const { 
      messages, 
      userId, 
      companyId, 
      conversationId, 
      messageId,
      assetId,
      assetPath,
      transcriptUrl,
      transcriptPath,
      userPrompt,
      fileName,
      fileType,
      hasTranscript,
      sceneId,
      projectId
    } = body;

    console.log('📡 [API-ROUTE] Received repurpose parameters:', { 
      assetId, 
      fileName, 
      fileType, 
      hasTranscript,
      transcriptUrl: transcriptUrl ? 'Available' : 'None',
      transcriptPath: transcriptPath ? 'Available' : 'None',
      userPrompt: userPrompt?.substring(0, 100) + '...',
      userId, 
      companyId,
      sceneId,
      projectId
    });

    // Validate required fields
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return new Response(
        JSON.stringify({ error: 'Messages array is required and cannot be empty' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    if (!userId || !companyId || !conversationId) {
      return new Response(
        JSON.stringify({ error: 'userId, companyId, and conversationId are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    if (!assetId || !assetPath || !fileName || !fileType) {
      return new Response(
        JSON.stringify({ error: 'assetId, assetPath, fileName, and fileType are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Forward request to sb-server
    const sbServerUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || 'http://localhost:8080';
    const response = await fetch(`${sbServerUrl}/repurpose-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
        body: JSON.stringify({
          messages,
          userId,
          companyId,
          conversationId,
          messageId,
          assetId,
          assetPath,
          transcriptUrl,
          transcriptPath,
          userPrompt,
          fileName,
          fileType,
          hasTranscript,
          sceneId,
          projectId
        }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error from sb-server:', errorText);
      return new Response(
        JSON.stringify({ error: 'Failed to get response from AI service' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Parse and return the JSON response
    const data = await response.json();
    return Response.json(data);

  } catch (error) {
    console.error('Error in repurpose chat API:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
