'use server';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

type LoggerContext = {
  name: string;
  bucket?: string;
  filePath?: string;
  companyId?: string;
};

/**
 * Generate signed URLs for private storage buckets with team access control
 * This ensures only team members can access assets uploaded by their team
 */
export async function createSignedUrl(
  bucket: string,
  filePath: string,
  expiresIn: number = 3600 // 1 hour default
): Promise<string> {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient();
  
  const ctx: LoggerContext = {
    name: 'create-signed-url',
    bucket,
    filePath,
  };

  try {
    // Check if bucket is public by getting bucket info
    const { data: buckets } = await supabase.storage.listBuckets();
    const bucketData = buckets?.find(b => b.id === bucket);
    
    if (bucketData?.public) {
      // For public buckets, return public URL
      const { data } = supabase.storage.from(bucket).getPublicUrl(filePath);
      return data.publicUrl;
    }

    // For private buckets, create signed URL
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(filePath, expiresIn);

    if (error) {
      logger.error({ ...ctx, error }, 'Failed to create signed URL');
      throw error;
    }

    if (!data?.signedUrl) {
      throw new Error('No signed URL returned');
    }

    logger.info({ ...ctx, expiresIn }, 'Successfully created signed URL');
    return data.signedUrl;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Error creating signed URL');
    throw error;
  }
}

/**
 * Generate multiple signed URLs in batch for better performance
 */
export async function createSignedUrls(
  bucket: string,
  filePaths: string[],
  expiresIn: number = 3600
): Promise<Record<string, string>> {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient();
  
  const ctx: LoggerContext = {
    name: 'create-signed-urls-batch',
    bucket,
  };

  try {
    // Check if bucket is public
    const { data: buckets } = await supabase.storage.listBuckets();
    const bucketData = buckets?.find(b => b.id === bucket);
    
    if (bucketData?.public) {
      // For public buckets, return public URLs
      const urls: Record<string, string> = {};
      filePaths.forEach(filePath => {
        const { data } = supabase.storage.from(bucket).getPublicUrl(filePath);
        urls[filePath] = data.publicUrl;
      });
      return urls;
    }

    // For private buckets, create signed URLs
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrls(filePaths, expiresIn);

    if (error) {
      logger.error({ ...ctx, error }, 'Failed to create signed URLs');
      throw error;
    }

    if (!data) {
      throw new Error('No signed URLs returned');
    }

    // Convert array response to object keyed by file path
    const urls: Record<string, string> = {};
    data.forEach((item, index) => {
      if (item.signedUrl && filePaths[index]) {
        urls[filePaths[index]] = item.signedUrl;
      }
    });

    logger.info({ ...ctx, count: filePaths.length, expiresIn }, 'Successfully created signed URLs');
    return urls;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Error creating signed URLs');
    throw error;
  }
}

/**
 * Get appropriate URL for a storage file (signed URL for private buckets, public URL for public buckets)
 */
export async function getStorageFileUrl(
  bucket: string,
  filePath: string,
  expiresIn: number = 3600
): Promise<string> {
  return createSignedUrl(bucket, filePath, expiresIn);
}

/**
 * Get URLs for multiple storage files with proper access control
 */
export async function getStorageFileUrls(
  bucket: string,
  filePaths: string[],
  expiresIn: number = 3600
): Promise<Record<string, string>> {
  return createSignedUrls(bucket, filePaths, expiresIn);
}

/**
 * Helper to determine the correct bucket and path for a given folder
 */
export async function resolveStorageBucketAndPath(
  companyId: string,
  folderName: string,
  fileName?: string
): Promise<{ bucket: string; path: string }> {
  let bucket = 'brand-assets';
  let path: string;

  if (folderName === 'logos') {
    path = `${companyId}/brand/${folderName}`;
  } else if (folderName.startsWith('generated/') || folderName === 'generated') {
    bucket = 'generated';
    path = `${companyId}/${folderName.replace('generated/', '')}`;
  } else {
    path = `${companyId}/assets/${folderName}`;
  }

  if (fileName) {
    path = `${path}/${fileName}`;
  }

  return { bucket, path };
}
