# AWS Lambda Video Rendering Setup

This guide will help you set up AWS Lambda video rendering for your video studio using Remotion Lambda.

## Prerequisites

1. **AWS Account**: You need an active AWS account
2. **Node.js**: Ensure you have Node.js installed
3. **pnpm**: Ensure you have pnpm installed (`npm install -g pnpm`)
4. **AWS Credentials**: Configure your AWS credentials as environment variables

## Quick Setup

Run the automated setup script:

```bash
cd apps/web
./setup-lambda.sh
```

## Manual Setup

### Step 1: Install Dependencies

```bash
cd apps/web
pnpm add @remotion/lambda@4.0.272 @remotion/lambda/client@4.0.272
```

### Step 2: Configure AWS Credentials

Create or update your `.env.local` file with your AWS credentials:

```bash
# AWS Credentials
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1

# Alternative Remotion-specific credentials (if using different account)
REMOTION_AWS_ACCESS_KEY_ID=your_aws_access_key_id
REMOTION_AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
```

**Note**: Remotion Lambda will automatically create all necessary AWS resources (IAM roles, S3 buckets, Lambda functions) using these credentials. No manual AWS CLI setup required!

### Step 3: Create Remotion Lambda Site

```bash
npx remotion lambda sites create components/video-editor/v7/remotion/lambda.tsx --site-name=axcels-video-studio --region=us-east-1
```

### Step 4: Deploy Lambda Function

```bash
npx remotion lambda functions deploy --region=us-east-1 --memory=2048 --disk=2048 --timeout=900
```

### Step 5: Update Constants

After successful deployment, update your constants with the actual values returned:

```typescript
// apps/web/components/video-editor/v7/constants.ts
export const SITE_NAME = "axcels-video-studio";
export const LAMBDA_FUNCTION_NAME = "remotion-render-xxxxx"; // From deployment output
export const REGION = "us-east-1";
export const BUCKET_NAME = "remotionlambda-xxxxx"; // From deployment output
```

## File Structure

The implementation includes these key files:

```
apps/web/
├── remotion.config.ts                           # Remotion configuration
├── components/video-editor/v7/remotion/
│   ├── index.ts                                 # Unified entry point (auto-detects environment)
│   ├── root.tsx                                 # Root components (regular + Lambda)
│   └── main.tsx                                 # Main component with Lambda support
├── app/api/video-editor/lambda/
│   ├── render/route.ts                          # Lambda render API endpoint
│   └── progress/route.ts                         # Lambda progress API endpoint
└── setup-lambda.sh                              # Automated setup script
```

## How It Works

1. **Unified Entry Point**: `index.ts` automatically detects the environment and registers the appropriate root component
2. **Environment Detection**: Uses `REMOTION_LAMBDA` env var or `AWS_LAMBDA_FUNCTION_NAME` to detect Lambda environment
3. **Lambda Root Component**: `RemotionLambdaRoot` sets `isLambdaRender: true` to disable interactive elements
4. **Main Component**: `main.tsx` conditionally renders interactive elements based on `isLambdaRender` flag
5. **API Routes**: Handle Lambda rendering requests and progress tracking
6. **Configuration**: `remotion.config.ts` sets up webpack aliases and rendering options

## Testing

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Navigate to the video studio and create/edit a video

3. Click the "Render Video" button - it should:
   - Show "Preparing..." status
   - Switch to "Rendering... X%" with progress
   - Complete with a download link

## Troubleshooting

### Common Issues

1. **"Lambda function not found"**
   - Ensure `LAMBDA_FUNCTION_NAME` matches the deployed function
   - Check that the function was deployed successfully

2. **"Access denied"**
   - Verify AWS credentials are correct
   - Check IAM permissions for Lambda and S3

3. **"Site not found"**
   - Verify `SITE_NAME` matches your Remotion site
   - Ensure the site was created successfully

4. **Module resolution errors**
   - The unified entry point automatically detects Lambda environment
   - Uses the same `index.ts` for both regular and Lambda rendering

### Debugging

1. **Check AWS CloudWatch Logs** for Lambda function execution
2. **Monitor S3 bucket** for rendered video files
3. **Check browser console** for client-side errors
4. **Verify environment variables** are set correctly

## Cost Optimization

1. **Memory Settings**: Start with 2048MB, adjust based on video complexity
2. **Timeout**: Set to 900 seconds (15 minutes) for longer videos
3. **Cleanup**: Regularly clean up old rendered videos from S3
4. **Monitoring**: Use AWS Cost Explorer to track usage

## Production Deployment

1. Set environment variables in your deployment platform
2. Ensure AWS credentials are properly configured
3. Update constants with production Lambda function names
4. Test thoroughly with different video lengths and complexities

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review AWS CloudWatch logs
3. Verify all environment variables are set
4. Ensure AWS permissions are correct
