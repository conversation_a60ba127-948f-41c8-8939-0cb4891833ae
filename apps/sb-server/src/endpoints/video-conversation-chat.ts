import { Request, Response } from 'express';
import { run } from '@openai/agents';
import { streamVideoConversationChat, ChatMessage, getSceneOverlays } from '../lib/utils/videoConversationAgent.js';

interface VideoConversationChatRequest {
  messages: ChatMessage[];
  userId: string;
  companyId: string;
  conversationId: string;
  messageId?: string;
  projectId?: string; // Keep for backwards compatibility
  sceneId?: string; // New scene-based architecture
}

export async function videoConversationChatHandler(req: Request, res: Response): Promise<void> {
  try {
    console.log(`🎬 [ENDPOINT] ===== VIDEO CONVERSATION CHAT START =====`);
    console.log(`🎬 [ENDPOINT] Request body keys:`, Object.keys(req.body));
    console.log(`🎬 [ENDPOINT] Request body:`, JSON.stringify(req.body, null, 2));
    
    const { messages, userId, companyId, conversationId, messageId, projectId, sceneId }: VideoConversationChatRequest = req.body;
    console.log(`🎬 [ENDPOINT] Extracted parameters:`);
    console.log(`  - userId: ${userId}`);
    console.log(`  - companyId: ${companyId}`);
    console.log(`  - conversationId: ${conversationId}`);
    console.log(`  - messageId: ${messageId}`);
    console.log(`  - projectId: ${projectId}`);
    console.log(`  - sceneId: ${sceneId}`);
    console.log(`  - messages count: ${messages?.length || 0}`);
    // Validate required fields
    console.log(`🔍 [ENDPOINT] Validating required fields...`);
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      console.error(`❌ [ENDPOINT] Validation failed: Messages array is invalid`);
      res.status(400).json({ 
        error: 'Messages array is required and cannot be empty' 
      });
      return;
    }

    if (!userId || !companyId || !conversationId) {
      console.error(`❌ [ENDPOINT] Validation failed: Missing required fields`);
      console.error(`  - userId: ${userId ? '✓' : '✗'}`);
      console.error(`  - companyId: ${companyId ? '✓' : '✗'}`);
      console.error(`  - conversationId: ${conversationId ? '✓' : '✗'}`);
      res.status(400).json({ 
        error: 'userId, companyId, and conversationId are required' 
      });
      return;
    }
    
    console.log(`✅ [ENDPOINT] Required fields validation passed`);

    // Validate message structure
    console.log(`🔍 [ENDPOINT] Validating message structure for ${messages.length} messages...`);
    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];
      console.log(`  - Message ${i + 1}: role="${msg?.role}", content length=${msg?.content?.length || 0}`);
      
      if (!msg.role) {
        console.error(`❌ [ENDPOINT] Message ${i + 1} validation failed: missing role or content`);
        res.status(400).json({ 
          error: 'Each message must have role and content' 
        });
        return;
      }
      if (!['user', 'assistant'].includes(msg.role)) {
        console.error(`❌ [ENDPOINT] Message ${i + 1} validation failed: invalid role "${msg.role}"`);
        res.status(400).json({ 
          error: 'Message role must be either "user" or "assistant"' 
        });
        return;
      }
    }

    // Ensure the latest message is from the user
    const latestMessage = messages[messages.length - 1];
    console.log(`🔍 [ENDPOINT] Latest message role: ${latestMessage.role}`);
    if (latestMessage.role !== 'user') {
      console.error(`❌ [ENDPOINT] Latest message validation failed: role is "${latestMessage.role}", expected "user"`);
      res.status(400).json({ 
        error: 'Latest message must be from user' 
      });
      return;
    }
    
    console.log(`✅ [ENDPOINT] Message structure validation passed`);

    console.log(`🚀 [ENDPOINT] Starting video conversation processing...`);

    // Validate that we have sceneId since it's required for the new architecture
    if (!sceneId) {
      console.error(`❌ [ENDPOINT] Scene ID is required for video conversation`);
      res.status(400).json({
        error: 'Scene ID is required for video conversation'
      });
      return;
    }

    // Fetch current overlays for the scene
    console.log(`🔍 [ENDPOINT] Fetching overlays for scene ${sceneId}...`);
    const sceneOverlays = await getSceneOverlays(sceneId);
    console.log(`📋 [ENDPOINT] Found ${sceneOverlays.length} overlays in scene`);

    // Create context for the video conversation agent
    const context = {
      userId,
      companyId,
      conversationId,
      messageId,
      projectId,
      sceneId,
      sceneOverlays, // Always provide current scene overlays
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        created_at: Date.now(),
      })),
    };
    
    console.log(`📋 [ENDPOINT] Created context:`, {
      userId: context.userId,
      companyId: context.companyId,
      conversationId: context.conversationId,
      messageId: context.messageId,
      projectId: context.projectId,
      sceneId: context.sceneId,
      sceneOverlaysCount: context.sceneOverlays.length,
      messagesCount: context.messages.length
    });

    // Get the agent and conversation details
    console.log(`🤖 [ENDPOINT] Creating video conversation agent...`);
    let agent, userMessage;
    try {
      const result = await streamVideoConversationChat({
        messages,
        context,
      });
      agent = result.agent;
      userMessage = result.userMessage;
    } catch (agentError) {
      console.error(`❌ [ENDPOINT] Failed to create video conversation agent:`, agentError);
      // Return a success response with error content so the frontend can handle it properly
      res.json({
        content: 'Sorry, an error occurred while setting up the video assistant. Please try again or contact support if the issue persists.',
        role: 'assistant',
        is_error: true,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    console.log(`📨 [ENDPOINT] User message extracted: "${userMessage}"`);
    console.log(`🏃 [ENDPOINT] Running agent with context...`);

    // Run the agent with the user's message
    let result;
    try {
      result = await run(agent, userMessage, {
        context: context,
      });
    } catch (runError) {
      console.error(`❌ [ENDPOINT] Failed to run video conversation agent:`, runError);
      // Return a success response with error content so the frontend can handle it properly
      res.json({
        content: 'Sorry, an error occurred while processing your request. Please try again or contact support if the issue persists.',
        role: 'assistant',
        is_error: true,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    console.log(`🎯 [ENDPOINT] Agent run completed. Result:`, {
      hasFinalOutput: !!result.finalOutput,
      finalOutputLength: result.finalOutput?.length || 0,
      finalOutputPreview: result.finalOutput?.substring(0, 100) + (result.finalOutput?.length > 100 ? '...' : '')
    });

    // Get the response
    let responseContent = result.finalOutput || '';
    console.log(`🔍 [ENDPOINT] Response content:`, responseContent);
    if (!responseContent) {
      console.log(`⚠️ [ENDPOINT] No response content, using default message`);
      responseContent = "I'm here to help you create amazing videos! Please describe what kind of video you'd like to generate, and I'll help you create the perfect prompt.";
      
      // Return early for default response
      console.log(`📤 [ENDPOINT] Sending default response`);
      res.json({
        content: responseContent,
        role: 'assistant',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    console.log(`✅ [ENDPOINT] Video conversation response generated (${responseContent.length} chars)`);
    console.log(`📝 [ENDPOINT] Response preview: ${responseContent.substring(0, 200)}${responseContent.length > 200 ? '...' : ''}`);

    // With the new tool-based architecture, the agent handles overlay manipulation directly
    // We just return the agent's natural language response
    console.log(`📤 [ENDPOINT] Returning agent response`);
    res.json({
      content: responseContent,
      role: 'assistant',
      timestamp: new Date().toISOString(),
    });

    console.log(`🎬 [ENDPOINT] ===== VIDEO CONVERSATION CHAT END =====`);

  } catch (error) {
    console.error('💥 [ENDPOINT] FATAL ERROR in video conversation chat:', {
      error: error.message,
      stack: error.stack,
      errorType: error.constructor.name,
      timestamp: new Date().toISOString()
    });
    
    console.log(`📤 [ENDPOINT] Sending error response as assistant message`);
    res.json({
      content: 'Sorry, an unexpected error occurred while processing your request. Please try again or contact support if the issue persists.',
      role: 'assistant',
      is_error: true,
      timestamp: new Date().toISOString(),
    });
    
    console.log(`🎬 [ENDPOINT] ===== VIDEO CONVERSATION CHAT END (ERROR) =====`);
  }
}
