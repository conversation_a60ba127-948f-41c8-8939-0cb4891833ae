import { Request, Response } from 'express';
import { run } from '@openai/agents';
import { streamGenerationChat, ChatMessage } from '../lib/utils/generationAgent.js';

interface GenerationChatRequest {
  messages: ChatMessage[];
  userId: string;
  companyId: string;
  conversationId: string;
  messageId?: string;
  // Video generation specific parameters
  videoType?: string;
  videoDuration?: number;
  sourceUrl?: string;
  userPrompt?: string;
  aspectRatio?: string;
}

export async function generationChatHandler(req: Request, res: Response): Promise<void> {
  try {
    console.log(`🎬 [GENERATION-ENDPOINT] ===== GENERATION CHAT START =====`);
    console.log(`🎬 [GENERATION-ENDPOINT] Request body keys:`, Object.keys(req.body));
    console.log(`🎬 [GENERATION-ENDPOINT] Request body:`, JSON.stringify(req.body, null, 2));
    
    const { 
      messages, 
      userId, 
      companyId, 
      conversationId, 
      messageId,
      videoType,
      videoDuration,
      sourceUrl,
      userPrompt,
      aspectRatio
    }: GenerationChatRequest = req.body;

    console.log(`🎬 [GENERATION-ENDPOINT] Extracted parameters:`);
    console.log(`  - userId: ${userId}`);
    console.log(`  - companyId: ${companyId}`);
    console.log(`  - conversationId: ${conversationId}`);
    console.log(`  - messageId: ${messageId}`);
    console.log(`  - videoType: ${videoType}`);
    console.log(`  - videoDuration: ${videoDuration}`);
    console.log(`  - sourceUrl: ${sourceUrl}`);
    console.log(`  - userPrompt: ${userPrompt}`);
    console.log(`  - aspectRatio: ${aspectRatio}`);
    console.log(`  - messages count: ${messages?.length || 0}`);

    // Validate required fields
    console.log(`🔍 [GENERATION-ENDPOINT] Validating required fields...`);
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      console.error(`❌ [GENERATION-ENDPOINT] Validation failed: Messages array is invalid`);
      res.status(400).json({ 
        error: 'Messages array is required and cannot be empty' 
      });
      return;
    }

    if (!userId || !companyId || !conversationId) {
      console.error(`❌ [GENERATION-ENDPOINT] Validation failed: Missing required fields`);
      console.error(`  - userId: ${userId ? '✓' : '✗'}`);
      console.error(`  - companyId: ${companyId ? '✓' : '✗'}`);
      console.error(`  - conversationId: ${conversationId ? '✓' : '✗'}`);
      res.status(400).json({ 
        error: 'userId, companyId, and conversationId are required' 
      });
      return;
    }

    console.log(`✅ [GENERATION-ENDPOINT] Required fields validation passed`);

    // Validate message structure
    console.log(`🔍 [GENERATION-ENDPOINT] Validating message structure for ${messages.length} messages...`);
    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];
      console.log(`  - Message ${i + 1}: role="${msg?.role}", content length=${msg?.content?.length || 0}`);
      
      if (!msg.role || !msg.content) {
        console.error(`❌ [GENERATION-ENDPOINT] Message ${i + 1} validation failed: missing role or content`);
        res.status(400).json({ 
          error: 'Each message must have role and content' 
        });
        return;
      }
      if (!['user', 'assistant'].includes(msg.role)) {
        console.error(`❌ [GENERATION-ENDPOINT] Message ${i + 1} validation failed: invalid role "${msg.role}"`);
        res.status(400).json({ 
          error: 'Message role must be either "user" or "assistant"' 
        });
        return;
      }
    }

    // Ensure the latest message is from the user
    const latestMessage = messages[messages.length - 1];
    console.log(`🔍 [GENERATION-ENDPOINT] Latest message role: ${latestMessage.role}`);
    if (latestMessage.role !== 'user') {
      console.error(`❌ [GENERATION-ENDPOINT] Latest message validation failed: role is "${latestMessage.role}", expected "user"`);
      res.status(400).json({ 
        error: 'Latest message must be from user' 
      });
      return;
    }

    console.log(`✅ [GENERATION-ENDPOINT] Message structure validation passed`);

    // If we have video generation parameters, enhance the user message
    let enhancedMessages = [...messages];
    if (videoType && videoDuration && sourceUrl) {
      console.log(`🎬 [GENERATION-ENDPOINT] Enhancing user message with video generation parameters`);
      
      const generationRequest = `Please generate video scenes with the following specifications:
- Video Type: ${videoType}
- Duration: ${videoDuration} seconds
- Source URL: ${sourceUrl}
- Aspect Ratio: ${aspectRatio || '16:9'}
${userPrompt ? `- Additional Requirements: ${userPrompt}` : ''}

${latestMessage.content}`;

      enhancedMessages[enhancedMessages.length - 1] = {
        ...latestMessage,
        content: generationRequest
      };

      console.log(`🎬 [GENERATION-ENDPOINT] Enhanced message:`, generationRequest);
    }

    console.log(`🚀 [GENERATION-ENDPOINT] Starting generation processing...`);

    // Create context for the generation agent
    const context = {
      userId,
      companyId,
      conversationId,
      messageId,
      messages: enhancedMessages.map(msg => ({
        role: msg.role,
        content: msg.content,
        created_at: Date.now(),
      })),
    };
    
    console.log(`📋 [GENERATION-ENDPOINT] Created context:`, {
      userId: context.userId,
      companyId: context.companyId,
      conversationId: context.conversationId,
      messageId: context.messageId,
      messagesCount: context.messages.length
    });

    // Get the agent and conversation details
    console.log(`🤖 [GENERATION-ENDPOINT] Creating generation agent...`);
    let agent, userMessage;
    try {
      const result = await streamGenerationChat({
        messages: enhancedMessages,
        context,
      });
      agent = result.agent;
      userMessage = result.userMessage;
    } catch (agentError) {
      console.error(`❌ [GENERATION-ENDPOINT] Failed to create generation agent:`, agentError);
      res.json({
        content: 'Sorry, an error occurred while setting up the video generation assistant. Please try again or contact support if the issue persists.',
        role: 'assistant',
        is_error: true,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    console.log(`📨 [GENERATION-ENDPOINT] User message extracted: "${userMessage}"`);
    console.log(`🏃 [GENERATION-ENDPOINT] Running agent with context...`);

    // Run the agent with the user's message
    let result;
    try {
      result = await run(agent, userMessage, {
        context: context,
      });
    } catch (runError) {
      console.error(`❌ [GENERATION-ENDPOINT] Failed to run generation agent:`, runError);
      res.json({
        content: 'Sorry, an error occurred while processing your video generation request. Please try again or contact support if the issue persists.',
        role: 'assistant',
        is_error: true,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    console.log(`🎯 [GENERATION-ENDPOINT] Agent run completed. Result:`, {
      hasFinalOutput: !!result.finalOutput,
      finalOutputLength: result.finalOutput?.length || 0,
      finalOutputPreview: result.finalOutput?.substring(0, 100) + (result.finalOutput?.length > 100 ? '...' : '')
    });

    // Get the response
    let responseContent = result.finalOutput || '';
    console.log(`🔍 [GENERATION-ENDPOINT] Response content length:`, responseContent.length);
    
    if (!responseContent) {
      console.log(`⚠️ [GENERATION-ENDPOINT] No response content, using default message`);
      responseContent = "I'm here to help you generate detailed video scenes for your promotional videos! Please provide the video type, duration, source URL, and any specific requirements you have.";
      
      console.log(`📤 [GENERATION-ENDPOINT] Sending default response`);
      res.json({
        content: responseContent,
        role: 'assistant',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    console.log(`✅ [GENERATION-ENDPOINT] Generation response created (${responseContent.length} chars)`);
    console.log(`📝 [GENERATION-ENDPOINT] Response preview: ${responseContent.substring(0, 200)}${responseContent.length > 200 ? '...' : ''}`);

    console.log(`📤 [GENERATION-ENDPOINT] Returning agent response`);
    res.json({
      content: responseContent,
      role: 'assistant',
      timestamp: new Date().toISOString(),
    });

    console.log(`🎬 [GENERATION-ENDPOINT] ===== GENERATION CHAT END =====`);

  } catch (error) {
    console.error('💥 [GENERATION-ENDPOINT] FATAL ERROR in generation chat:', {
      error: error.message,
      stack: error.stack,
      errorType: error.constructor.name,
      timestamp: new Date().toISOString()
    });
    
    console.log(`📤 [GENERATION-ENDPOINT] Sending error response as assistant message`);
    res.json({
      content: 'Sorry, an unexpected error occurred while processing your video generation request. Please try again or contact support if the issue persists.',
      role: 'assistant',
      is_error: true,
      timestamp: new Date().toISOString(),
    });
    
    console.log(`🎬 [GENERATION-ENDPOINT] ===== GENERATION CHAT END (ERROR) =====`);
  }
}
