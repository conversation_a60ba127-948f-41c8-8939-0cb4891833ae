import { Request, Response } from 'express';
import { run } from '@openai/agents';
import { streamRepurposeChat, ChatMessage } from '../lib/utils/repurposeAgent.js';

interface RepurposeChatRequest {
  messages: ChatMessage[];
  userId: string;
  companyId: string;
  conversationId: string;
  messageId?: string;
  // Repurpose-specific parameters
  assetId: string;
  assetPath: string;
  transcriptUrl?: string;
  transcriptPath?: string;
  userPrompt: string;
  fileName: string;
  fileType: string;
  hasTranscript?: boolean;
  // Database context for direct overlay insertion
  sceneId?: string;
  projectId?: string;
}

export async function repurposeChatHandler(req: Request, res: Response): Promise<void> {
  try {
    console.log(`🎬 [REPURPOSE-ENDPOINT] ===== REPURPOSE CHAT START =====`);
    console.log(`🎬 [REPURPOSE-ENDPOINT] Request body keys:`, Object.keys(req.body));
    console.log(`🎬 [REPURPOSE-ENDPOINT] Request body:`, JSON.stringify(req.body, null, 2));
    
    const { 
      messages, 
      userId, 
      companyId, 
      conversationId, 
      messageId,
      assetId,
      assetPath,
      transcriptUrl,
      transcriptPath,
      userPrompt,
      fileName,
      fileType,
      hasTranscript,
      sceneId,
      projectId
    }: RepurposeChatRequest = req.body;

    console.log(`🎬 [REPURPOSE-ENDPOINT] Extracted parameters:`);
    console.log(`  - userId: ${userId}`);
    console.log(`  - companyId: ${companyId}`);
    console.log(`  - conversationId: ${conversationId}`);
    console.log(`  - sceneId: ${sceneId}`);
    console.log(`  - projectId: ${projectId}`);
    console.log(`  - messageId: ${messageId}`);
    console.log(`  - assetId: ${assetId}`);
    console.log(`  - fileName: ${fileName}`);
    console.log(`  - fileType: ${fileType}`);
    console.log(`  - hasTranscript: ${hasTranscript}`);
    console.log(`  - transcriptUrl: ${transcriptUrl}`);
    console.log(`  - transcriptPath: ${transcriptPath}`);
    console.log(`  - userPrompt length: ${userPrompt?.length || 0}`);
    console.log(`  - messages count: ${messages?.length || 0}`);

    // Validate required fields
    console.log(`🔍 [REPURPOSE-ENDPOINT] Validating required fields...`);
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      console.error(`❌ [REPURPOSE-ENDPOINT] Validation failed: Messages array is invalid`);
      res.status(400).json({ 
        error: 'Messages array is required and cannot be empty' 
      });
      return;
    }

    if (!userId || !companyId || !conversationId) {
      console.error(`❌ [REPURPOSE-ENDPOINT] Validation failed: Missing required fields`);
      console.error(`  - userId: ${userId ? '✓' : '✗'}`);
      console.error(`  - companyId: ${companyId ? '✓' : '✗'}`);
      console.error(`  - conversationId: ${conversationId ? '✓' : '✗'}`);
      res.status(400).json({ 
        error: 'userId, companyId, and conversationId are required' 
      });
      return;
    }

    if (!assetId || !assetPath || !fileName || !fileType) {
      console.error(`❌ [REPURPOSE-ENDPOINT] Validation failed: Missing asset fields`);
      console.error(`  - assetId: ${assetId ? '✓' : '✗'}`);
      console.error(`  - assetPath: ${assetPath ? '✓' : '✗'}`);
      console.error(`  - fileName: ${fileName ? '✓' : '✗'}`);
      console.error(`  - fileType: ${fileType ? '✓' : '✗'}`);
      res.status(400).json({ 
        error: 'assetId, assetPath, fileName, and fileType are required' 
      });
      return;
    }

    console.log(`✅ [REPURPOSE-ENDPOINT] Required fields validation passed`);

    // Validate message structure
    console.log(`🔍 [REPURPOSE-ENDPOINT] Validating message structure for ${messages.length} messages...`);
    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];
      console.log(`  - Message ${i + 1}: role="${msg?.role}", content length=${msg?.content?.length || 0}`);
      
      if (!msg.role || !msg.content) {
        console.error(`❌ [REPURPOSE-ENDPOINT] Message ${i + 1} validation failed: missing role or content`);
        res.status(400).json({ 
          error: 'Each message must have role and content' 
        });
        return;
      }
      if (!['user', 'assistant'].includes(msg.role)) {
        console.error(`❌ [REPURPOSE-ENDPOINT] Message ${i + 1} validation failed: invalid role "${msg.role}"`);
        res.status(400).json({ 
          error: 'Message role must be either "user" or "assistant"' 
        });
        return;
      }
    }

    // Ensure the latest message is from the user
    const latestMessage = messages[messages.length - 1];
    console.log(`🔍 [REPURPOSE-ENDPOINT] Latest message role: ${latestMessage.role}`);
    if (latestMessage.role !== 'user') {
      console.error(`❌ [REPURPOSE-ENDPOINT] Latest message validation failed: role is "${latestMessage.role}", expected "user"`);
      res.status(400).json({ 
        error: 'Latest message must be from user' 
      });
      return;
    }

    console.log(`✅ [REPURPOSE-ENDPOINT] Message structure validation passed`);

    // Enhance the user message with repurpose parameters
    let enhancedMessages = [...messages];
    console.log(`🎬 [REPURPOSE-ENDPOINT] Enhancing user message with repurpose parameters`);
    
    // Download and process the transcript content
    let processedTranscript = '';
    if (hasTranscript && transcriptUrl) {
      try {
        console.log('🎬 [REPURPOSE-ENDPOINT] Downloading transcript from:', transcriptUrl);
        const transcriptResponse = await fetch(transcriptUrl);
        if (transcriptResponse.ok) {
          const rawTranscriptContent = await transcriptResponse.text();
          console.log('🎬 [REPURPOSE-ENDPOINT] Raw transcript downloaded:', {
            length: rawTranscriptContent.length,
            preview: rawTranscriptContent.substring(0, 200) + '...'
          });

          // Parse the JSON transcript file
          try {
            const transcriptData = JSON.parse(rawTranscriptContent);
            console.log('🎬 [REPURPOSE-ENDPOINT] Transcript JSON parsed:', {
              hasFullTranscript: !!transcriptData.fullTranscript,
              hasDetailedTranscript: !!transcriptData.detailedTranscript,
              detailedSections: transcriptData.detailedTranscript?.length || 0
            });

            // Extract and process the detailedTranscript array
            if (transcriptData.detailedTranscript && Array.isArray(transcriptData.detailedTranscript)) {
              // Remove the words arrays to reduce size and keep only transcript text with timing info
              const cleanedSections = transcriptData.detailedTranscript
                .filter(section => section.transcript && section.transcript.trim().length > 0)
                .map((section, index) => ({
                  sectionNumber: index + 1,
                  transcript: section.transcript,
                  confidence: section.confidence,
                  // Extract timing from first and last words if available
                  startTime: section.words && section.words.length > 0 ? 
                    (section.words[0].startTime?.low || 0) : null,
                  endTime: section.words && section.words.length > 0 ? 
                    (section.words[section.words.length - 1].endTime?.low || 0) : null
                }));

              processedTranscript = JSON.stringify(cleanedSections, null, 2);
              
              console.log('🎬 [REPURPOSE-ENDPOINT] Transcript processed successfully:', {
                originalLength: rawTranscriptContent.length,
                processedLength: processedTranscript.length,
                sectionsExtracted: cleanedSections.length,
                sizereduction: `${Math.round((1 - processedTranscript.length / rawTranscriptContent.length) * 100)}%`
              });
            } else {
              throw new Error('No detailedTranscript array found in transcript file');
            }
          } catch (parseError) {
            console.error('❌ [REPURPOSE-ENDPOINT] Error parsing transcript JSON:', parseError);
            res.json({
              content: 'Sorry, the transcript file format is invalid. Please ensure the file contains a valid transcript.',
              role: 'assistant',
              is_error: true,
              timestamp: new Date().toISOString(),
            });
            return;
          }
        } else {
          console.error(`❌ [REPURPOSE-ENDPOINT] Failed to download transcript: HTTP ${transcriptResponse.status}`);
          res.json({
            content: 'Sorry, I could not download the transcript for analysis. Please ensure the file has a valid transcript.',
            role: 'assistant',
            is_error: true,
            timestamp: new Date().toISOString(),
          });
          return;
        }
      } catch (error) {
        console.error('❌ [REPURPOSE-ENDPOINT] Error downloading transcript:', error);
        res.json({
          content: 'Sorry, I encountered an error while downloading the transcript. Please try again.',
          role: 'assistant',
          is_error: true,
          timestamp: new Date().toISOString(),
        });
        return;
      }
    } else {
      console.error('❌ [REPURPOSE-ENDPOINT] No transcript available for analysis');
      res.json({
        content: 'Sorry, this file does not have a transcript available. Transcripts are required for repurposing videos.',
        role: 'assistant',
        is_error: true,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    const repurposeRequest = `Please analyze and repurpose the following video/audio content:

**File Information:**
- File: ${fileName}
- Type: ${fileType}
- Asset ID: ${assetId}
- Asset Path: ${assetPath}

**Repurpose Instructions:**
${userPrompt}

**Transcript Sections:**
The transcript is structured as sections with timing information. Each section contains:
- sectionNumber: Sequential section number
- transcript: The spoken text in that section
- confidence: Speech recognition confidence (0-1)
- startTime: Start time in seconds (from first word)
- endTime: End time in seconds (from last word)

${processedTranscript}

${latestMessage.content}

Please analyze these transcript sections and extract strategic clips based on the repurpose instructions above. Use the timing information to create precise start/end times for each clip. Use the repurposeVideo tool with the clips you extract.`;

    enhancedMessages[enhancedMessages.length - 1] = {
      ...latestMessage,
      content: repurposeRequest
    };

    console.log(`🎬 [REPURPOSE-ENDPOINT] Enhanced message created`);

    console.log(`🚀 [REPURPOSE-ENDPOINT] Starting repurpose processing...`);

    // Create context for the repurpose agent
    const context = {
      userId,
      companyId,
      conversationId,
      messageId,
      sceneId,
      projectId,
      messages: enhancedMessages.map(msg => ({
        role: msg.role,
        content: msg.content,
        created_at: Date.now(),
      })),
      transcriptUrl,
      transcriptPath,
      userPrompt,
      fileName,
      fileType,
      hasTranscript
    };
    
    console.log(`📋 [REPURPOSE-ENDPOINT] Created context:`, {
      userId: context.userId,
      companyId: context.companyId,
      conversationId: context.conversationId,
      messageId: context.messageId,
      messagesCount: context.messages.length
    });

    // Get the agent and conversation details
    console.log(`🤖 [REPURPOSE-ENDPOINT] Creating repurpose agent...`);
    let agent, userMessage;
    try {
      const result = await streamRepurposeChat({
        messages: enhancedMessages,
        context,
      });
      agent = result.agent;
      userMessage = result.userMessage;
    } catch (agentError) {
      console.error(`❌ [REPURPOSE-ENDPOINT] Failed to create repurpose agent:`, agentError);
      res.json({
        content: 'Sorry, an error occurred while setting up the video repurpose assistant. Please try again or contact support if the issue persists.',
        role: 'assistant',
        is_error: true,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    console.log(`📨 [REPURPOSE-ENDPOINT] User message extracted: "${userMessage?.substring(0, 100)}..."`);
    console.log(`🏃 [REPURPOSE-ENDPOINT] Running agent with context...`);

    // Run the agent with the user's message
    let result;
    try {
      result = await run(agent, userMessage, {
        context: {
          ...context,
          // Add repurpose-specific context for the tool
          assetId,
          assetPath,
          fileName,
          fileType,
          processedTranscript,
          // Add database context for direct overlay insertion
          sceneId: context.sceneId,
          projectId: context.projectId,
          userId: context.userId
        },
      });
    } catch (runError) {
      console.error(`❌ [REPURPOSE-ENDPOINT] Failed to run repurpose agent:`, runError);
      res.json({
        content: 'Sorry, an error occurred while processing your video repurpose request. Please try again or contact support if the issue persists.',
        role: 'assistant',
        is_error: true,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    console.log(`🎯 [REPURPOSE-ENDPOINT] Agent run completed. Result:`, {
      hasFinalOutput: !!result.finalOutput,
      finalOutputLength: result.finalOutput?.length || 0,
      finalOutputPreview: result.finalOutput?.substring(0, 100) + (result.finalOutput?.length > 100 ? '...' : '')
    });

    // Get the response
    let responseContent = result.finalOutput || '';
    console.log(`🔍 [REPURPOSE-ENDPOINT] Response content length:`, responseContent.length);
    
    if (!responseContent) {
      console.log(`⚠️ [REPURPOSE-ENDPOINT] No response content, using default message`);
      responseContent = "I'm here to help you repurpose your video and audio content! Please provide specific instructions on how you'd like to repurpose your selected file.";
      
      console.log(`📤 [REPURPOSE-ENDPOINT] Sending default response`);
      res.json({
        content: responseContent,
        role: 'assistant',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    console.log(`✅ [REPURPOSE-ENDPOINT] Repurpose response created (${responseContent.length} chars)`);
    console.log(`📝 [REPURPOSE-ENDPOINT] Response preview: ${responseContent.substring(0, 200)}${responseContent.length > 200 ? '...' : ''}`);

    console.log(`📤 [REPURPOSE-ENDPOINT] Returning agent response`);
    
    // Overlays are now inserted directly into the database by the tool
    // No need to return them in the API response
    console.log(`🎬 [REPURPOSE-ENDPOINT] Overlays were inserted directly into database via Zero sync`);
    
    res.json({
      content: responseContent,
      role: 'assistant',
      timestamp: new Date().toISOString(),
    });

    console.log(`🎬 [REPURPOSE-ENDPOINT] ===== REPURPOSE CHAT END =====`);

  } catch (error) {
    console.error('💥 [REPURPOSE-ENDPOINT] FATAL ERROR in repurpose chat:', {
      error: error.message,
      stack: error.stack,
      errorType: error.constructor.name,
      timestamp: new Date().toISOString()
    });
    
    console.log(`📤 [REPURPOSE-ENDPOINT] Sending error response as assistant message`);
    res.json({
      content: 'Sorry, an unexpected error occurred while processing your video repurpose request. Please try again or contact support if the issue persists.',
      role: 'assistant',
      is_error: true,
      timestamp: new Date().toISOString(),
    });
    
    console.log(`🎬 [REPURPOSE-ENDPOINT] ===== REPURPOSE CHAT END (ERROR) =====`);
  }
}
