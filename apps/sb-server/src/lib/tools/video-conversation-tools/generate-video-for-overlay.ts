import postgres from 'postgres';
import dotenv from 'dotenv';
import { generateVideo } from '../../services/generate-video.js';

// Helper function to validate and normalize video duration
function validateVideoDuration(duration: number): number {
  const supportedDurations = [4, 6, 8];
  
  // If the duration is already supported, return it
  if (supportedDurations.includes(duration)) {
    return duration;
  }
  
  // Find the closest supported duration
  const closest = supportedDurations.reduce((prev, curr) => 
    Math.abs(curr - duration) < Math.abs(prev - duration) ? curr : prev
  );
  
  console.log(`⚠️ [TOOL] Duration ${duration}s not supported, using closest: ${closest}s`);
  return closest;
}

dotenv.config();

// Initialize postgres client
const sql = postgres(process.env.ZERO_UPSTREAM_DB! as string);

export interface VideoConversationContext {
  userId: string;
  companyId: string;
  conversationId: string;
  messageId?: string;
  projectId?: string;
  sceneId: string;
}

export async function generateVideoForOverlay(
  overlayId: string,
  prompt: string,
  duration: number = 8,
  aspectRatio: string = '9:16',
  context?: VideoConversationContext
): Promise<{ success: boolean; message: string }> {
  // Validate and normalize the duration
  const validatedDuration = validateVideoDuration(duration);
  console.log(`🎥 [TOOL] Starting video generation for overlay ${overlayId}:`, { prompt, duration: validatedDuration, aspectRatio });

  try {
    // Get overlay details to get context info
    const overlay = await sql`
      SELECT o.*, s.project_id
      FROM video_project_overlays o
      JOIN video_project_scenes s ON s.id = o.scene_id
      WHERE o.id = ${overlayId}
    `.then(result => result[0]);

    if (!overlay) {
      throw new Error(`Overlay ${overlayId} not found`);
    }

    // Get project details for company_id and user_id
    const project = await sql`
      SELECT account_id, user_id FROM video_projects WHERE id = ${overlay.project_id}
    `.then(result => result[0]);

    if (!project) {
      throw new Error(`Project ${overlay.project_id} not found`);
    }

    await generateVideo({
      video_prompt: prompt,
      duration: validatedDuration,
      model: 'veo-3-fast',
      aspect_ratio: aspectRatio,
      company_id: project.account_id,
      user_id: project.user_id,
      conversation_id: context?.conversationId,
      message_id: context?.messageId,
      overlay_id: overlayId,
      scene_id: overlay.scene_id,
      project_id: overlay.project_id,
    });

    console.log(`✅ [TOOL] Video generation started for overlay ${overlayId}`);
    return { success: true, message: `Video generation started for overlay ${overlayId}` };
  } catch (error) {
    console.error(`❌ [TOOL] Video generation failed for overlay ${overlayId}:`, error);
    return { success: false, message: `Video generation failed: ${error.message}` };
  }
}
