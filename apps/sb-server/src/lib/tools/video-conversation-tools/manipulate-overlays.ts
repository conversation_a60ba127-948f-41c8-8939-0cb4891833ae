import { v4 as uuidv4 } from 'uuid';
import postgres from 'postgres';
import dotenv from 'dotenv';
import { Overlay } from '../../types/overlay-types.js';
import { generateVideoForOverlay, type VideoConversationContext } from './generate-video-for-overlay.js';

dotenv.config();

// Initialize postgres client
const sql = postgres(process.env.ZERO_UPSTREAM_DB! as string);

// Helper function to validate and normalize video duration
function validateVideoDuration(duration: number): number {
  const supportedDurations = [4, 6, 8];
  
  // If the duration is already supported, return it
  if (supportedDurations.includes(duration)) {
    return duration;
  }
  
  // Find the closest supported duration
  const closest = supportedDurations.reduce((prev, curr) => 
    Math.abs(curr - duration) < Math.abs(prev - duration) ? curr : prev
  );
  
  console.log(`⚠️ [TOOL] Duration ${duration}s not supported, using closest: ${closest}s`);
  return closest;
}

// Helper function to ensure overlay styles have proper default values
function normalizeOverlayStyles(styles: any, overlayType: string): any {
  const baseStyles = {
    fontSize: "",
    fontWeight: "",
    color: "",
    backgroundColor: "",
    fontFamily: "",
    textAlign: null,
    fill: "",
    stroke: "",
    strokeWidth: 0,
    borderRadius: "",
    opacity: 1,
    zIndex: 2,
    objectFit: "cover",
    volume: 1,
    transform: "",
    filter: "",
    boxShadow: "",
    textShadow: "",
    padding: "",
    border: "",
    animation: null, // Explicitly set to null instead of undefined
    ...styles
  };

  // Ensure animation is properly structured if provided
  if (baseStyles.animation && typeof baseStyles.animation === 'object') {
    baseStyles.animation = {
      enter: baseStyles.animation.enter || null,
      exit: baseStyles.animation.exit || null,
      ...baseStyles.animation
    };
  }

  return baseStyles;
}

export type OverlayOperation =
  | { action: 'create'; overlay: Omit<Overlay, 'id' | 'scene_id' | 'project_id' | 'created_at' | 'updated_at'> }
  | { action: 'update'; id: string; updates: Partial<Overlay> }
  | { action: 'delete'; id: string };

export type OverlayManipulationResult = {
  success: boolean;
  results: Array<{
    action: string;
    id?: string;
    success: boolean;
    error?: string;
  }>;
  message: string;
};

// Single tool function for all overlay manipulations
export async function manipulateOverlays(
  sceneId: string,
  operations: OverlayOperation[],
  context?: VideoConversationContext
): Promise<OverlayManipulationResult> {
  console.log(`🔧 [TOOL] Processing ${operations.length} overlay operations for scene ${sceneId}`);
  console.log(`🔧 [TOOL] Operations: ${JSON.stringify(operations, null, 2)}`);

  const results: OverlayManipulationResult['results'] = [];
  const now = Date.now();

  // Get scene info to get project_id
  const scene = await sql`
    SELECT project_id FROM video_project_scenes WHERE id = ${sceneId}
  `.then(result => result[0]);

  if (!scene) {
    return {
      success: false,
      results: [],
      message: `Scene ${sceneId} not found`
    };
  }

  // Process each operation
  for (const operation of operations) {
    try {
      switch (operation.action) {
        case 'create': {
          const overlayId = uuidv4();
          const normalizedStyles = normalizeOverlayStyles((operation.overlay as any).styles || {}, operation.overlay.type);
          const overlay = {
            ...operation.overlay,
            id: overlayId,
            scene_id: sceneId,
            project_id: scene.project_id,
            styles: normalizedStyles
          };

          await sql`
            INSERT INTO video_project_overlays (
              id, scene_id, project_id, type, content, "left", top, width, height,
              row, "from", "durationInFrames", rotation, "isDragging", loading,
              styles, src, "videoStartTime", duration, video_prompt, transcript,
              "startFromSound", speed, captions, template, category
            ) VALUES (
              ${overlay.id}, ${overlay.scene_id}, ${overlay.project_id},
              ${overlay.type}, ${(overlay as any).content || ''}, ${overlay.left},
              ${overlay.top}, ${overlay.width}, ${overlay.height},
              ${overlay.row}, ${overlay.from}, ${overlay.durationInFrames},
              ${overlay.rotation}, ${overlay.isDragging}, ${overlay.loading},
              ${overlay.styles}, ${(overlay as any).src || ''},
              ${(overlay as any).videoStartTime || 0}, ${(overlay as any).duration || 6},
              ${(overlay as any).video_prompt || ''}, ${(overlay as any).transcript || ''},
              ${(overlay as any).startFromSound || 0}, ${(overlay as any).speed || 1},
              ${(overlay as any).captions || []}, ${(overlay as any).template || ''},
              ${(overlay as any).category || ''}
            )
          `;

          results.push({ action: 'create', id: overlayId, success: true });
          console.log(`✅ [TOOL] Created overlay ${overlayId}`);

          // If this is a video overlay with a video_prompt, automatically trigger video generation
          if (overlay.type === 'video' && (overlay as any).video_prompt && context) {
            console.log(`🎥 [TOOL] Auto-triggering video generation for overlay ${overlayId}`);
            try {
              const requestedDuration = (overlay as any).duration || 6;
              const validatedDuration = validateVideoDuration(requestedDuration);
              
              const videoResult = await generateVideoForOverlay(
                overlayId,
                (overlay as any).video_prompt,
                validatedDuration,
                '16:9', // Default aspect ratio for the canvas
                context
              );
              console.log(`🎥 [TOOL] Video generation result: ${videoResult.message}`);
            } catch (videoError) {
              console.error(`❌ [TOOL] Failed to trigger video generation for overlay ${overlayId}:`, videoError);
              // Don't fail the overlay creation if video generation fails
            }
          }
          break;
        }

        case 'update': {
            console.log(`🔧 [TOOL] Operations: ${JSON.stringify(operations, null, 2)}`);
          const { id, scene_id, project_id, created_at, ...updateFields } = operation.updates as any;

          if (Object.keys(updateFields).length === 0) {
            results.push({ action: 'update', id: operation.id, success: false, error: 'No fields to update' });
            continue;
          }

          // Build update query dynamically
          const setFields: string[] = [];
          const values: any[] = [operation.id]; // $1 for WHERE clause
          let paramIndex = 2;

          Object.entries(updateFields).forEach(([key, value]) => {
            if (value !== undefined) {
              const dbColumn = key; // Use camelCase column names directly

              setFields.push(`"${dbColumn}" = $${paramIndex}`);
              values.push(value);
              paramIndex++;
            }
          });

          setFields.push(`updated_at = $${paramIndex}`);
          values.push(now);

          const query = `
            UPDATE video_project_overlays
            SET ${setFields.join(', ')}
            WHERE id = $1
          `;

          await sql.unsafe(query, values);
          results.push({ action: 'update', id: operation.id, success: true });
          console.log(`✅ [TOOL] Updated overlay ${operation.id}`);
          break;
        }

        case 'delete': {
          await sql`DELETE FROM video_project_overlays WHERE id = ${operation.id}`;
          results.push({ action: 'delete', id: operation.id, success: true });
          console.log(`✅ [TOOL] Deleted overlay ${operation.id}`);
          break;
        }

        default:
          results.push({
            action: (operation as any).action,
            success: false,
            error: 'Unknown action'
          });
      }
    } catch (error) {
      console.error(`❌ [TOOL] Error processing ${operation.action} operation:`, error);
      results.push({
        action: operation.action,
        id: 'id' in operation ? operation.id : undefined,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  const successCount = results.filter(r => r.success).length;
  const failureCount = results.filter(r => !r.success).length;

  return {
    success: failureCount === 0,
    results,
    message: `Processed ${operations.length} operations: ${successCount} successful, ${failureCount} failed`
  };
}
