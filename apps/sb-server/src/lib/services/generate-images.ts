import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import crypto from 'crypto';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

// Types for image generation
interface ImageGenerationParams {
  image_prompt: string;
  aspect_ratio: string;
  company_id: string;
  image_gen_styles?: any;
  brand_context?: string;
}

interface ImageGenerationResponse {
  url: string;
  path?: string;
  metadata?: {
    model: string;
    timestamp: string;
    company_id: string;
    prompt_hash: string;
  };
}

// Supported image generation models (actual image generation models)
const IMAGE_GENERATION_MODEL = "google/gemini-2.5-flash-image-preview"

// Add this helper function to format image styles
function formatImageStyles(imageGenStyles: any): string {
  if (!imageGenStyles || typeof imageGenStyles !== 'object') {
    return "Professional, clean, modern aesthetic";
  }

  const styleParts: string[] = [];
  
  // Extract key style elements
  if (imageGenStyles.image_styles) {
    styleParts.push(`Style: ${imageGenStyles.image_styles}`);
  }
  
  if (imageGenStyles.image_modes) {
    styleParts.push(`Mode: ${imageGenStyles.image_modes}`);
  }
  
  if (imageGenStyles.image_lighting) {
    styleParts.push(`Lighting: ${imageGenStyles.image_lighting}`);
  }
  
  if (imageGenStyles.image_details) {
    styleParts.push(`Detail Level: ${imageGenStyles.image_details}`);
  }
  
  if (imageGenStyles.image_color_balance) {
    styleParts.push(`Color: ${imageGenStyles.image_color_balance}`);
  }
  
  if (imageGenStyles.image_camera_angle_perspective) {
    styleParts.push(`Camera Angle: ${imageGenStyles.image_camera_angle_perspective}`);
  }
  
  if (imageGenStyles.image_mood_atmosphere) {
    styleParts.push(`Mood: ${imageGenStyles.image_mood_atmosphere}`);
  }
  
  if (imageGenStyles.artistic_influences_references) {
    styleParts.push(`Influences: ${imageGenStyles.artistic_influences_references}`);
  }

  return styleParts.length > 0 
    ? styleParts.join(', ') 
    : "Professional, clean, modern aesthetic";
}

/**
 * Generates an image using Langfuse prompts and OpenRouter with proper image generation models
 */
export async function generateImages(params: ImageGenerationParams): Promise<ImageGenerationResponse> {
  try {
    const { image_prompt, aspect_ratio, company_id, image_gen_styles, brand_context } = params;

    // Validate required fields
    if (!image_prompt) {
      throw new Error('Missing required field: image_prompt');
    }

    if (!company_id) {
      throw new Error('Missing required field: company_id');
    }

    // Get Langfuse prompt template
    const prompt = await langfuse.getPrompt("generate_image_v1", undefined, { label: "production" });

    // Prepare template variables
    const templateVariables = {
      image_prompt,
      brand_brief: brand_context || "Professional marketing brand with modern aesthetic",
      image_gen_styles: formatImageStyles(image_gen_styles),
      aspect_ratio: aspect_ratio || "16:9"
    };

    // Compile the prompt
    const compiledPrompt = prompt.compile(templateVariables);


    // Generate image using OpenRouter API directly
    const imageData = await generateImageWithOpenRouter(compiledPrompt);

    // Generate metadata
    const promptHash = crypto.createHash('md5').update(compiledPrompt).digest('hex');
    const metadata = {
      model: IMAGE_GENERATION_MODEL,
      timestamp: new Date().toISOString(),
      company_id,
      prompt_hash: promptHash
    };

    // Upload image and return response
    const uploadResult = await uploadImage(imageData, metadata);

    return {
      ...uploadResult,
      metadata
    };

  } catch (error) {

    throw new Error(`Failed to generate image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate image using OpenRouter API with Gemini 2.5 Flash Image Preview
 */
async function generateImageWithOpenRouter(prompt: string): Promise<Buffer> {
  try {


    if (!process.env.OPENROUTER_API_KEY) {
      throw new Error('OPENROUTER_API_KEY environment variable is required');
    }

    // OpenRouter API call for image generation - CRITICAL: Must include modalities parameter
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.OPENROUTER_REFERER || 'https://axcels.com',
        'X-Title': 'Axcels AI Image Generation'
      },
      body: JSON.stringify({
        model: IMAGE_GENERATION_MODEL,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        modalities: ["image", "text"], // REQUIRED for image generation
        max_tokens: 1024,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      const errorText = await response.text();

      throw new Error(`OpenRouter API request failed with status ${response.status}: ${errorText}`);
    }

    const data = await response.json();


    // Extract image data from response according to OpenRouter docs
    const message = data.choices?.[0]?.message;
    if (!message) {
      throw new Error('No message in OpenRouter response');
    }

    // Check for images array in the message (correct format per docs)
    if (!message.images || !Array.isArray(message.images) || message.images.length === 0) {
      throw new Error('No images found in OpenRouter response. Make sure the model supports image generation.');
    }

    // Get the first image from the response
    const firstImage = message.images[0];
    if (!firstImage.image_url || !firstImage.image_url.url) {
      throw new Error('Invalid image format in OpenRouter response');
    }

    // Extract base64 data from data URL (format: "data:image/png;base64,...")
    const imageDataUrl = firstImage.image_url.url;
    if (!imageDataUrl.startsWith('data:image/')) {
      throw new Error('Expected base64 data URL format from OpenRouter');
    }

    // Extract the base64 part after the comma
    const base64Data = imageDataUrl.split(',')[1];
    if (!base64Data) {
      throw new Error('No base64 data found in image URL');
    }

    // Validate base64 format
    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(base64Data)) {

      throw new Error('Invalid base64 format in OpenRouter response');
    }

    // Convert to buffer
    const imageBuffer = Buffer.from(base64Data, 'base64');


    return imageBuffer;

  } catch (error) {

    throw new Error(`Failed to generate image with OpenRouter: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload image to storage and return URL
 * Dev: Uses ImgBB for public URLs (localhost Supabase URLs don't work externally)
 * Production: Uses Supabase storage for proper asset library organization
 */
async function uploadImage(imageBuffer: Buffer, metadata: any): Promise<ImageGenerationResponse> {
  try {
    const isProduction = process.env.NODE_ENV === 'production';
    console.log(`Uploading generated image (${isProduction ? 'production' : 'development'} mode)...`);

    if (isProduction && process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      // Production: Use Supabase storage for proper asset library
      const result = await uploadToSupabase(imageBuffer, metadata.company_id);
      return {
        url: result.url,
        path: result.path,
        metadata
      };
    } else {
      // Development: Use ImgBB for public URLs + Supabase for asset library
      // Also fallback for production if Supabase credentials are missing
      if (!isProduction) {
        console.log('Development mode: Using ImgBB for publicly accessible URLs');
        
        // Upload to ImgBB for public URL
        const imgbbResult = await uploadToImgBB(imageBuffer);
        
        // ALSO upload to Supabase for asset library organization (if available)
        try {
          if (process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
            console.log('Also saving to Supabase asset library...');
            await uploadToSupabase(imageBuffer, metadata.company_id, 'generated');
            console.log('Successfully saved to both ImgBB and Supabase asset library');
          } else {
            console.log('Supabase credentials not available, skipping asset library save');
          }
        } catch (supabaseError) {
          console.warn('Failed to save to Supabase asset library (non-critical):', supabaseError);
        }
        
        return {
          url: imgbbResult.url,
          path: imgbbResult.url, // For compatibility
          metadata
        };
      } else {
        console.warn('Production mode but Supabase credentials missing, falling back to ImgBB');
        const result = await uploadToImgBB(imageBuffer);
        return {
          url: result.url,
          path: result.url, // For compatibility
          metadata
        };
      }
    }

  } catch (error) {
    console.error('Error uploading generated image:', error);
    throw new Error(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload image to Supabase Storage with proper asset library organization
 */
async function uploadToSupabase(imageBuffer: Buffer, companyId: string, imageType: 'generated' | 'edited' = 'generated'): Promise<{ url: string; path: string }> {
  try {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables are required');
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Generate unique filename with type prefix
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const uniqueId = crypto.randomUUID();
    const fileName = `${imageType}-${uniqueId}-${timestamp}.png`;
    const filePath = `${companyId}/generated/${fileName}`;

    console.log(`Uploading ${imageType} image to Supabase:`, { fileName, filePath, bucketName: 'generated' });

    // Upload to Supabase storage in the 'generated' bucket
    const { error: uploadError } = await supabase.storage
      .from('generated')
      .upload(filePath, imageBuffer, {
        contentType: 'image/png',
        cacheControl: '3600',
        upsert: true
      });

    if (uploadError) {
      console.error('Supabase upload error:', uploadError);
      throw uploadError;
    }

    // Create public URL (no token needed since bucket is public)
    const publicUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/generated/${filePath}`;
    
    console.log('Successfully uploaded to Supabase:', { publicUrl, filePath });
    return { url: publicUrl, path: filePath };

  } catch (error) {
    console.error('Error uploading to Supabase:', error);
    throw new Error(`Failed to upload to Supabase: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload image to ImgBB (development environment)
 */
async function uploadToImgBB(imageBuffer: Buffer): Promise<{ url: string }> {
  const imgbbApiKey = process.env.IMGBB_API_KEY;

  if (!imgbbApiKey) {
    throw new Error('IMGBB_API_KEY environment variable is required');
  }

  // Convert buffer to base64 string
  const base64Image = imageBuffer.toString('base64');

  // Create form data using URLSearchParams for Node.js compatibility
  const formData = new URLSearchParams();
  formData.append('image', base64Image);

  const imgbbRes = await fetch(`https://api.imgbb.com/1/upload?key=${imgbbApiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: formData,
  });

  if (!imgbbRes.ok) {
    const errorText = await imgbbRes.text();

    throw new Error(`ImgBB upload failed with status ${imgbbRes.status}: ${errorText}`);
  }

  const imgbbData = await imgbbRes.json();


  if (!imgbbData.success) {
    throw new Error(`ImgBB upload reported failure: ${JSON.stringify(imgbbData)}`);
  }

  return { url: imgbbData.data.url };
}

