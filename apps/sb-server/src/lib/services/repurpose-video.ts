import dotenv from 'dotenv';
import { randomUUID } from 'crypto';

dotenv.config();

// Constants for video overlay creation (matching video editor constants)
const FRAMES_PER_SECOND = 30; // Standard 30 FPS
const DEFAULT_VIDEO_WIDTH = 1280; // Match video editor VIDEO_WIDTH
const DEFAULT_VIDEO_HEIGHT = 720; // Match video editor VIDEO_HEIGHT

interface RepurposeVideoInput {
  assetId: string;
  assetPath: string;
  transcriptUrl?: string;
  transcriptPath?: string;
  transcriptContent?: string; // Direct transcript content
  userPrompt: string;
  fileName: string;
  fileType: string;
  hasTranscript?: boolean;
}

interface VideoClip {
  clipNumber: number;
  title: string;
  startTime: number; // in seconds
  endTime: number;   // in seconds
  duration: number;  // in seconds
  description: string;
  keyMessage: string;
  reasoning: string;
}

interface VideoOverlay {
  id: string;
  type: 'video';
  from: number; // frame number (where on timeline)
  durationInFrames: number; // clip length in frames
  row: number; // timeline row
  left: number;
  top: number;
  width: number;
  height: number;
  src: string; // asset path
  videoStartTime: number; // start time in original video (seconds)
  duration: number; // clip duration in seconds
  content: string; // clip title
  isDragging: boolean;
  rotation: number;
  loading?: boolean;
  styles: {
    objectFit: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
    objectPosition: string;
    volume: number;
    opacity: number;
    zIndex: number;
  };
  metadata?: {
    clipTitle: string;
    clipDescription: string;
    keyMessage: string;
    reasoning: string;
  };
}

interface RepurposeVideoOutput {
  success: boolean;
  clips: VideoClip[];
  overlays: VideoOverlay[]; // Ready-to-use overlay structures
  transcriptContent?: string;
  originalDuration?: number;
  totalClipsDuration: number;
  summary: string;
  error?: string;
}

/**
 * Repurpose Video Service
 * 
 * This service takes a video/audio file with transcript and creates
 * strategic clips based on the user's repurpose prompt.
 * 
 * For now, this is a mock implementation that will be replaced
 * with actual AI-powered analysis.
 */
export async function repurposeVideo(input: RepurposeVideoInput, agentClips?: VideoClip[]): Promise<RepurposeVideoOutput> {
  try {
    console.log('🎬 [REPURPOSE-SERVICE] Starting video repurpose process...');
    console.log('🎬 [REPURPOSE-SERVICE] Input:', {
      input,
      assetId: input.assetId,
      fileName: input.fileName,
      fileType: input.fileType,
      hasTranscript: input.hasTranscript,
      transcriptUrl: input.transcriptUrl ? 'Available' : 'None',
      transcriptPath: input.transcriptPath ? 'Available' : 'None',
      userPrompt: input.userPrompt,
      hasAgentClips: !!agentClips
    });

    let transcriptContent = '';
    
    // Use provided transcript content or fetch if URL is available
    if (input.transcriptContent) {
      transcriptContent = input.transcriptContent;
      console.log('🎬 [REPURPOSE-SERVICE] Using provided transcript content:', {
        length: transcriptContent.length,
        preview: transcriptContent.substring(0, 100) + '...'
      });
    } else if (input.hasTranscript && input.transcriptUrl) {
      try {
        console.log('🎬 [REPURPOSE-SERVICE] Fetching transcript from:', input.transcriptUrl);
        const transcriptResponse = await fetch(input.transcriptUrl);
        
        if (transcriptResponse.ok) {
          transcriptContent = await transcriptResponse.text();
          console.log('🎬 [REPURPOSE-SERVICE] Transcript fetched successfully:', {
            length: transcriptContent.length,
            preview: transcriptContent.substring(0, 100) + '...'
          });
        } else {
          console.warn('🎬 [REPURPOSE-SERVICE] Failed to fetch transcript:', transcriptResponse.status);
          throw new Error(`Failed to fetch transcript: HTTP ${transcriptResponse.status}`);
        }
      } catch (error) {
        console.error('🎬 [REPURPOSE-SERVICE] Error fetching transcript:', error);
        throw new Error('Could not download transcript for analysis');
      }
    } else {
      console.warn('🎬 [REPURPOSE-SERVICE] No transcript available for analysis');
      throw new Error('Transcript is required for repurposing but not available');
    }

    // Use clips from agent if provided, otherwise create mock clips
    const clips = agentClips || [
      {
        clipNumber: 1,
        title: "Opening Hook",
        startTime: 0,
        endTime: 15,
        duration: 15,
        description: "Engaging opening that captures attention and sets the tone",
        keyMessage: "Introduction and hook to draw viewers in",
        reasoning: "First 15 seconds are crucial for retention and engagement"
      },
      {
        clipNumber: 2,
        title: "Main Content Highlight",
        startTime: 45,
        endTime: 90,
        duration: 45,
        description: "Core content that delivers the main value proposition",
        keyMessage: "Primary message and key insights",
        reasoning: "This section contains the most valuable information for the audience"
      },
      {
        clipNumber: 3,
        title: "Key Takeaway",
        startTime: 120,
        endTime: 150,
        duration: 30,
        description: "Important conclusion or actionable insight",
        keyMessage: "Clear takeaway and next steps",
        reasoning: "Provides closure and actionable value for viewers"
      }
    ];

    console.log('🎬 [REPURPOSE-SERVICE] Creating overlay structures from clips...');
    
    // Create overlay structures for the frontend
    const overlays = createVideoOverlays(clips, input.assetPath);
    
    const totalClipsDuration = clips.reduce((total, clip) => total + clip.duration, 0);

    const result: RepurposeVideoOutput = {
      success: true,
      clips,
      overlays,
      transcriptContent: transcriptContent.substring(0, 500) + '...', // Include preview
      originalDuration: Math.max(...clips.map(c => c.endTime)), // Estimate from clips
      totalClipsDuration,
      summary: `Created ${clips.length} clips totaling ${totalClipsDuration} seconds from the original content. Each clip is strategically selected to maximize engagement and deliver key value based on your repurpose instructions.`
    };

    console.log('🎬 [REPURPOSE-SERVICE] Analysis complete:', {
      clipsGenerated: result.clips.length,
      totalDuration: result.totalClipsDuration,
      originalDuration: result.originalDuration
    });

    // Log detailed clip information
    console.log('🎬 [REPURPOSE-SERVICE] Generated Clips:');
    result.clips.forEach((clip, index) => {
      console.log(`\n📹 Clip ${clip.clipNumber}: ${clip.title}`);
      console.log(`   ⏱️  Time: ${clip.startTime}s - ${clip.endTime}s (${clip.duration}s)`);
      console.log(`   🎯 Key Message: ${clip.keyMessage}`);
      console.log(`   📝 Description: ${clip.description}`);
      console.log(`   💡 Reasoning: ${clip.reasoning}`);
    });

    return result;

  } catch (error) {
    console.error('🚨 [REPURPOSE-SERVICE] Error during video repurpose:', error);
    return {
      success: false,
      clips: [],
      overlays: [],
      totalClipsDuration: 0,
      summary: 'Failed to repurpose video content',
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Create video overlay structures from clips for the frontend
 * These overlays are ready to be added directly to the timeline
 * All clips are placed sequentially on the same row (row 0)
 */
function createVideoOverlays(clips: VideoClip[], assetPath: string): VideoOverlay[] {
  console.log('🎬 [REPURPOSE-SERVICE] Creating overlays for', clips.length, 'clips');
  console.log('🎬 [REPURPOSE-SERVICE] Placing all clips sequentially on row 0');
  console.log('🎬 [REPURPOSE-SERVICE] Using overlay dimensions:', { 
    width: DEFAULT_VIDEO_WIDTH, 
    height: DEFAULT_VIDEO_HEIGHT 
  });
  
  let cumulativeFrames = 0; // Track the cumulative position for sequential placement
  
  return clips.map((clip, index) => {
    const durationInFrames = Math.round(clip.duration * FRAMES_PER_SECOND);
    const fromFrame = cumulativeFrames; // Start this clip where the previous one ended
    
    console.log(`🎬 [REPURPOSE-SERVICE] Clip ${index + 1}: "${clip.title}" - Frame ${fromFrame} to ${fromFrame + durationInFrames} (${clip.duration}s)`);
    
    const overlay: VideoOverlay = {
      id: randomUUID(),
      type: 'video',
      from: fromFrame, // Sequential placement: this clip starts where previous ended
      durationInFrames: durationInFrames,
      row: 0, // ALL clips on row 0 for sequential playback
      left: 0, // Video position in canvas
      top: 0,
      width: DEFAULT_VIDEO_WIDTH,
      height: DEFAULT_VIDEO_HEIGHT,
      src: assetPath, // Path to the original asset
      videoStartTime: clip.startTime, // Where to start reading from original video
      duration: clip.duration, // Clip duration in seconds
      content: clip.title, // Display title
      isDragging: false,
      rotation: 0,
      loading: false,
      styles: {
        objectFit: 'contain' as const,
        objectPosition: 'center center',
        volume: 1.0,
        opacity: 1,
        zIndex: 1,
      },
      metadata: {
        clipTitle: clip.title,
        clipDescription: clip.description,
        keyMessage: clip.keyMessage,
        reasoning: clip.reasoning
      }
    };
    
    // Update cumulative position for the next clip
    cumulativeFrames += durationInFrames;
    
    console.log(`🎬 [REPURPOSE-SERVICE] Created overlay for clip ${clip.clipNumber}:`, {
      id: overlay.id,
      from: overlay.from,
      videoStartTime: overlay.videoStartTime,
      duration: overlay.duration,
      durationInFrames: overlay.durationInFrames,
      row: overlay.row,
      type: overlay.type
    });
    
    return overlay;
  });
}
