// Define overlay types to match frontend exactly
export enum OverlayType {
  TEXT = 'text',
  IMAGE = 'image',
  SHAPE = 'shape',
  VIDEO = 'video',
  SOUND = 'sound',
  CAPTION = 'caption',
  LOCAL_DIR = 'local-dir',
  STICKER = 'sticker',
  TEMPLATE = 'template',
  CHAT = 'chat',
  GENERATE = 'generate',
}

export type BaseOverlay = {
  id: string;
  durationInFrames: number;
  from: number;
  height: number;
  row: number;
  left: number;
  top: number;
  width: number;
  isDragging: boolean;
  rotation: number;
  type: OverlayType;
  loading?: boolean;
  scene_id?: string;
  project_id?: string;
  created_at?: number;
  updated_at?: number;
};

export type BaseStyles = {
  opacity?: number;
  zIndex?: number;
  transform?: string;
};

export type AnimationConfig = {
  enter?: string;
  exit?: string;
};

export type TextOverlay = BaseOverlay & {
  type: OverlayType.TEXT;
  content: string;
  styles: BaseStyles & {
    fontSize: string;
    fontWeight: string;
    color: string;
    backgroundColor: string;
    fontFamily: string;
    fontStyle: string;
    textDecoration: string;
    lineHeight?: string;
    letterSpacing?: string;
    textAlign?: 'left' | 'center' | 'right';
    textShadow?: string;
    padding?: string;
    paddingBackgroundColor?: string;
    borderRadius?: string;
    boxShadow?: string;
    background?: string;
    WebkitBackgroundClip?: string;
    WebkitTextFillColor?: string;
    backdropFilter?: string;
    border?: string;
    animation?: AnimationConfig;
  };
};

export type ShapeOverlay = BaseOverlay & {
  type: OverlayType.SHAPE;
  content: string;
  styles: BaseStyles & {
    fill?: string;
    stroke?: string;
    strokeWidth?: number;
    borderRadius?: string;
    boxShadow?: string;
    gradient?: string;
  };
};

export type VideoOverlay = BaseOverlay & {
  type: OverlayType.VIDEO;
  content: string;
  src: string;
  transcript?: string;
  videoStartTime?: number;
  duration?: number;
  speed?: number;
  video_prompt?: string; // For AI-generated videos
  styles: BaseStyles & {
    objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
    objectPosition?: string;
    volume?: number;
    borderRadius?: string;
    filter?: string;
    boxShadow?: string;
    border?: string;
    padding?: string;
    paddingBackgroundColor?: string;
    animation?: AnimationConfig;
  };
};

// TTS specific types for robust transcript support
export type TTSWord = {
  word: string;
  start: number; // in seconds
  end: number; // in seconds
};

export type TTSTranscriptSegment = {
  transcript: string;
  confidence: number;
  words: TTSWord[];
  startTime?: number;
  endTime?: number;
};

export type TTSTranscript = {
  originalText: string;
  fullTranscript: string;
  detailedTranscript: TTSTranscriptSegment[];
  duration: number;
  wordCount: number;
  averageConfidence: number;
  generatedAt: string;
  language: string;
  voiceId: string;
};

export type SoundOverlay = BaseOverlay & {
  type: OverlayType.SOUND;
  content: string;
  src: string;
  transcript?: string | TTSTranscript; // Can be simple string or robust transcript object
  startFromSound?: number;
  styles: BaseStyles & {
    volume?: number;
  };
};

export type CaptionWord = {
  word: string;
  startMs: number;
  endMs: number;
  confidence: number;
};

export type Caption = {
  text: string;
  startMs: number;
  endMs: number;
  timestampMs: number | null;
  confidence: number | null;
  words: CaptionWord[];
};

export interface CaptionStyles {
  fontFamily: string;
  fontSize: string;
  lineHeight: number;
  textAlign: 'left' | 'center' | 'right';
  color: string;
  backgroundColor?: string;
  background?: string;
  backdropFilter?: string;
  padding?: string;
  fontWeight?: number | string;
  letterSpacing?: string;
  textShadow?: string;
  borderRadius?: string;
  transition?: string;
  highlightStyle?: {
    backgroundColor?: string;
    color?: string;
    scale?: number;
    fontWeight?: number;
    textShadow?: string;
    padding?: string;
    borderRadius?: string;
    transition?: string;
    background?: string;
    border?: string;
    backdropFilter?: string;
  };
}

export interface CaptionOverlay extends BaseOverlay {
  type: OverlayType.CAPTION;
  captions: Caption[];
  styles?: CaptionStyles;
  template?: string;
}

export type ImageOverlay = BaseOverlay & {
  type: OverlayType.IMAGE;
  src: string;
  content?: string;
  styles: BaseStyles & {
    filter?: string;
    borderRadius?: string;
    objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
    objectPosition?: string;
    boxShadow?: string;
    border?: string;
    padding?: string;
    paddingBackgroundColor?: string;
    animation?: AnimationConfig;
  };
};

export type StickerCategory =
  | "Shapes"
  | "Discounts"
  | "Emojis"
  | "Reviews"
  | "Default";

export type StickerOverlay = BaseOverlay & {
  type: OverlayType.STICKER;
  content: string;
  category: StickerCategory;
  styles: BaseStyles & {
    fill?: string;
    stroke?: string;
    strokeWidth?: number;
    scale?: number;
    filter?: string;
    animation?: AnimationConfig;
  };
};

export type Overlay =
  | TextOverlay
  | ImageOverlay
  | ShapeOverlay
  | VideoOverlay
  | SoundOverlay
  | CaptionOverlay
  | StickerOverlay;
