import { Agent, run, tool } from '@openai/agents';
import { z } from 'zod';
import postgres from 'postgres';
import dotenv from 'dotenv';
import {
  OverlayType,
  BaseOverlay,
  TextOverlay,
  ShapeOverlay,
  VideoOverlay,
  SoundOverlay,
  CaptionOverlay,
  ImageOverlay,
  StickerOverlay,
  Overlay,
  Caption,
  CaptionWord,
  CaptionStyles,
  TTSTranscript,
  TTSTranscriptSegment,
  TTSWord,
  StickerCategory,
  AnimationConfig,
  BaseStyles
} from '../types/overlay-types.js';
import { 
  manipulateOverlays, 
  type OverlayOperation, 
  type OverlayManipulationResult 
} from '../tools/video-conversation-tools/manipulate-overlays.js';
import { 
  type VideoConversationContext as VideoContext
} from '../tools/video-conversation-tools/generate-video-for-overlay.js';

dotenv.config();

// Initialize postgres client
const sql = postgres(process.env.ZERO_UPSTREAM_DB! as string);

interface VideoConversationContext extends VideoContext {
  sceneOverlays: Overlay[]; // Always provide current overlays
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    created_at: number;
  }>;
}




export async function createVideoConversationAgent(context: VideoConversationContext) {
  const systemInstructions = `You are an AI assistant for a Video Editor application with direct overlay manipulation tools.

**YOUR TOOLS:**
You have access to these overlay manipulation tools:
1. **manipulate_overlays** - Create, update, or delete multiple overlays in one operation. When creating video overlays, AI video generation will be automatically triggered.

**CURRENT SCENE STATE:**
You always have access to the current overlays in the scene. You can see all existing overlays, their properties, positioning, and timing. Use this information to:
- Avoid overlaps on the same row and start/end times when placing new overlays. You can place on the same row (e.g. text) but not the same start/end times.
- When adding video overlays, the timing should never overlap with other overlays.
- Reference existing overlays when making updates
- Understand the current scene composition

**WORKFLOW:**
1. Analyze current scene overlays (provided in context)
2. Plan all overlay changes needed for the user's request
3. Use manipulate_overlays with an array of operations to make all changes at once
4. Video overlays with video_prompt will automatically trigger AI video generation
5. Always respond with what you did, not JSON structures

**EFFICIENT OPERATIONS:**
- Instead of making many separate tool calls, batch all your overlay changes into one manipulate_overlays call
- Each operation specifies: action ('create', 'update', or 'delete'), and the relevant data
- This allows you to make complex scene changes efficiently in a single operation
- When Updating video overlays - regenerate the full overlay as it is, and only update the changes you need to make. Make sure you include the preivous values as they are when no changes to that field of the overlay needs to be made. 

**OVERLAY TYPES:**
- **text**: Text overlays with styling and animations
- **shape**: Backgrounds, rectangles, circles (fill, stroke, etc.)
- **video**: Real people/actors or complex scenes (set loading: true initially)
  - Video durations must be 4, 6, or 8 seconds (other values will be rounded to nearest supported duration)

**POSITIONING:**
- Canvas size: 1280×720 (16:9 aspect ratio) - ALWAYS keep overlays within these bounds
- X coordinates: 0-1280 (width), Y coordinates: 0-720 (height)
- Rows are timeline tracks (higher row = higher zIndex)
- Use 'from' for start time, 'durationInFrames' for duration (30fps)
- Ensure no overlapping overlays on same row
- Position text overlays in visible areas (e.g., top: 100-600, left: 100-1800)

**STYLING EXAMPLES:**
Text styles: { fontSize: "48px", color: "#FFFFFF", fontFamily: "Arial", textAlign: "center", opacity: 1, zIndex: 10 }
Shape styles: { fill: "#000000", opacity: 1, zIndex: 1, borderRadius: "0px" }
Video styles: { opacity: 1, zIndex: 5, objectFit: "cover" }

**Context:**
- Scene ID: ${context.sceneId}
- User ID: ${context.userId}
- Company ID: ${context.companyId}
- Project ID: ${context.projectId}
- Current overlays in scene: ${context.sceneOverlays.length} overlays

**Current Scene Overlays:**
${context.sceneOverlays.map(o => `- ${o.type} overlay (ID: ${o.id}) at row ${o.row}, frames ${o.from}-${o.from + o.durationInFrames}`).join('\n')}


**IMPORTANT:**
- Use tools to manipulate overlays directly
- Don't return JSON - take action with tools
- Respond with natural language about what you did
- For video generation, use descriptive prompts with camera movements and style`;

  const agent = new Agent({
    name: 'Video Studio Assistant',
    instructions: systemInstructions,
    model: 'gpt-5',
    tools: [
      tool({
        name: 'manipulate_overlays',
        description: 'Create, update, or delete multiple overlays in one efficient operation',
        parameters: z.object({
          operations: z.array(z.union([
            z.object({
              action: z.literal('create'),
              overlay: z.object({
                type: z.enum(['text', 'shape', 'video']).describe('Type of overlay'),
                // type: z.enum(['text', 'shape', 'video', 'image', 'sound', 'caption', 'sticker']).describe('Type of overlay'),
                content: z.string().describe('Text content or shape type or description'),
                left: z.number().describe('X position (0-1920, keep within canvas bounds)'),
                top: z.number().describe('Y position (0-1080, keep within canvas bounds)'),
                width: z.number().describe('Width in pixels'),
                height: z.number().describe('Height in pixels'),
                row: z.number().describe('Timeline row (0=top layer)'),
                from: z.number().describe('Start frame'),
                durationInFrames: z.number().describe('Duration in frames (30fps), '),
                rotation: z.number().default(0).describe('Rotation in degrees'),
                isDragging: z.boolean().default(false).describe('Whether currently being dragged'),
                loading: z.boolean().default(false).describe('Whether loading (true for videos awaiting generation)'),
                styles: z.object({
                  fontSize: z.string().nullable().default(null),
                  fontWeight: z.string().nullable().default(null),
                  color: z.string().nullable().default(null),
                  backgroundColor: z.string().nullable().default(null),
                  fontFamily: z.string().nullable().default(null),
                  textAlign: z.enum(['left', 'center', 'right']).nullable().default(null),
                  fill: z.string().nullable().default(null),
                  stroke: z.string().nullable().default(null),
                  strokeWidth: z.number().nullable().default(null),
                  borderRadius: z.string().nullable().default(null),
                  opacity: z.number().nullable().default(null),
                  zIndex: z.number().nullable().default(null),
                  objectFit: z.enum(['contain', 'cover', 'fill', 'none', 'scale-down']).nullable().default(null),
                  volume: z.number().nullable().default(null),
                  transform: z.string().nullable().default(null),
                  filter: z.string().nullable().default(null),
                  boxShadow: z.string().nullable().default(null),
                  textShadow: z.string().nullable().default(null),
                  padding: z.string().nullable().default(null),
                  border: z.string().nullable().default(null)
                }).describe('Styling object with various CSS properties'),
                src: z.string().nullable().default(null).describe('Source URL'),
                transcript: z.string().nullable().default(null).describe('Transcript text'),
                video_prompt: z.string().nullable().default(null).describe('AI video generation prompt'),
                duration: z.number().nullable().default(null).describe('Duration in seconds'),
                videoStartTime: z.number().nullable().default(null).describe('Video start time'),
                startFromSound: z.number().nullable().default(null).describe('Sound start time'),
                speed: z.number().nullable().default(null).describe('Playback speed'),
                captions: z.array(z.object({
                  text: z.string(),
                  startMs: z.number(),
                  endMs: z.number(),
                  timestampMs: z.number().nullable(),
                  confidence: z.number().nullable(),
                  words: z.array(z.object({
                    word: z.string(),
                    startMs: z.number(),
                    endMs: z.number(),
                    confidence: z.number()
                  }))
                })).nullable().default(null).describe('Caption data with timing and words'),
                template: z.string().nullable().default(null).describe('Template name'),
                category: z.string().nullable().default(null).describe('Category')
              })
            }),
            z.object({
              action: z.literal('update'),
              id: z.string().describe('ID of overlay to update'),
              updates: z.object({
                left: z.number().nullable().default(null).describe('X position (0-1920, keep within canvas bounds)'),
                top: z.number().nullable().default(null).describe('Y position (0-1080, keep within canvas bounds)'),
                width: z.number().nullable().default(null),
                height: z.number().nullable().default(null),
                row: z.number().nullable().default(null),
                from: z.number().nullable().default(null),
                durationInFrames: z.number().nullable().default(null),
                rotation: z.number().nullable().default(null),
                styles: z.object({
                  fontSize: z.string().nullable().default(null),
                  fontWeight: z.string().nullable().default(null),
                  color: z.string().nullable().default(null),
                  backgroundColor: z.string().nullable().default(null),
                  fontFamily: z.string().nullable().default(null),
                  textAlign: z.enum(['left', 'center', 'right']).nullable().default(null),
                  fill: z.string().nullable().default(null),
                  stroke: z.string().nullable().default(null),
                  strokeWidth: z.number().nullable().default(null),
                  borderRadius: z.string().nullable().default(null),
                  opacity: z.number().nullable().default(null),
                  zIndex: z.number().nullable().default(null),
                  objectFit: z.enum(['contain', 'cover', 'fill', 'none', 'scale-down']).nullable().default(null),
                  volume: z.number().nullable().default(null),
                  transform: z.string().nullable().default(null),
                  filter: z.string().nullable().default(null),
                  boxShadow: z.string().nullable().default(null),
                  textShadow: z.string().nullable().default(null),
                  padding: z.string().nullable().default(null),
                  border: z.string().nullable().default(null)
                }).nullable().default(null),
                duration: z.number().nullable().default(null),
                videoStartTime: z.number().nullable().default(null)
              }).describe('Fields to update')
            }),
            z.object({
              action: z.literal('delete'),
              id: z.string().describe('ID of overlay to delete')
            })
          ])).describe('Array of overlay operations to perform')
        }),
        execute: async (params) => manipulateOverlays(context.sceneId, params.operations, context)
      }),
    ]
  });

  return agent;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface StreamChatParams {
  messages: ChatMessage[];
  context: VideoConversationContext;
}

// Function to fetch scene overlays from database
export async function getSceneOverlays(sceneId: string): Promise<Overlay[]> {
  console.log(`🔍 [DB] Getting overlays for scene ${sceneId}`);

  const overlays = await sql`
    SELECT * FROM video_project_overlays
    WHERE scene_id = ${sceneId}
    ORDER BY created_at ASC
  `;

  console.log(`✅ [DB] Found ${overlays.length} overlays for scene ${sceneId}`);

  // Convert database rows to proper overlay objects
  const typedOverlays: Overlay[] = overlays.map((row: any) => ({
    id: row.id,
    type: row.type as OverlayType,
    content: row.content || '',
    left: row.left || 0,
    top: row.top || 0,
    width: row.width || 100,
    height: row.height || 100,
    row: row.row || 0,
    from: row.from || 0,
    durationInFrames: row.durationInFrames || 30,
    rotation: row.rotation || 0,
    isDragging: row.isDragging || false,
    loading: row.loading || false,
    src: row.src || '',
    transcript: row.transcript,
    videoStartTime: row.videoStartTime,
    startFromSound: row.startFromSound,
    duration: row.duration,
    speed: row.speed,
    video_prompt: row.video_prompt,
    captions: row.captions ? (typeof row.captions === 'string' ? JSON.parse(row.captions) : row.captions) : [],
    template: row.template,
    category: row.category,
    styles: row.styles ? (typeof row.styles === 'string' ? JSON.parse(row.styles) : row.styles) : {},
    scene_id: row.scene_id,
    project_id: row.project_id,
    created_at: row.created_at,
    updated_at: row.updated_at,
  } as Overlay));

  return typedOverlays;
}

export async function streamVideoConversationChat(params: StreamChatParams) {
  const { messages, context } = params;
  console.log({messages})
  try {
    const agent = await createVideoConversationAgent(context);
    // get the overlay from the database
    // Convert messages to the format expected by the agent
    const conversationHistory = messages.map(msg => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content,
    }));

    // Get the latest user message
    const latestMessage = messages[messages.length - 1];
    if (!latestMessage || latestMessage.role !== 'user') {
      throw new Error('Latest message must be from user');
    }

    return {
      agent,
      userMessage: latestMessage.content,
      conversationHistory,
    };
  } catch (error) {
    console.error('Error creating video conversation agent:', error);
    throw new Error('Failed to create video conversation agent. Please try again.');
  }
}
