import { Agent, tool } from '@openai/agents';
import { z } from 'zod';
import { scrapeWebsite } from '../services/scrape-website.js';
import dotenv from 'dotenv';

dotenv.config();

interface GenerationContext {
  userId: string;
  companyId: string;
  conversationId: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    created_at: number;
  }>;
}

// Define website content analysis schema
const ContentAnalysisSchema = z.object({
  websiteUrl: z.string().describe('The analyzed website URL'),
  contentSummary: z.string().describe('Brief description of what the website offers'),
  keyFeaturesBenefits: z.array(z.string()).describe('List of main selling points and benefits'),
  targetAudience: z.string().describe('Who the product/service is for'),
  toneBrandVoice: z.string().describe('Brand voice style (Professional, casual, energetic, etc.)')
});

// Helper function to extract key features and benefits from content
function extractKeyFeatures(content: string): string[] {
  const features = [];
  const lowerContent = content.toLowerCase();
  
  // Look for common benefit/feature indicators
  const featureIndicators = [
    'benefit', 'feature', 'advantage', 'solution', 'service', 'product',
    'offer', 'provide', 'help', 'save', 'improve', 'increase', 'reduce',
    'fast', 'easy', 'simple', 'secure', 'reliable', 'affordable'
  ];
  
  // Split content into sentences and analyze
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
  
  for (const sentence of sentences) {
    const lowerSentence = sentence.toLowerCase();
    const hasFeatureIndicator = featureIndicators.some(indicator => 
      lowerSentence.includes(indicator)
    );
    
    if (hasFeatureIndicator && features.length < 5) {
      features.push(sentence.trim());
    }
  }
  
  // If no features found, extract first few meaningful sentences
  if (features.length === 0) {
    features.push(...sentences.slice(0, 3).map(s => s.trim()));
  }
  
  return features.slice(0, 5); // Limit to 5 features
}

// Helper function to analyze target audience from content
function analyzeTargetAudience(content: string): string {
  const lowerContent = content.toLowerCase();
  
  // Business/Professional indicators
  if (lowerContent.includes('business') || lowerContent.includes('enterprise') || 
      lowerContent.includes('company') || lowerContent.includes('professional')) {
    return 'Businesses and professionals looking for enterprise solutions';
  }
  
  // Consumer/Individual indicators
  if (lowerContent.includes('personal') || lowerContent.includes('individual') || 
      lowerContent.includes('home') || lowerContent.includes('family')) {
    return 'Individual consumers and families';
  }
  
  // Developer/Technical indicators
  if (lowerContent.includes('developer') || lowerContent.includes('api') || 
      lowerContent.includes('technical') || lowerContent.includes('code')) {
    return 'Developers and technical professionals';
  }
  
  // Small business indicators
  if (lowerContent.includes('small business') || lowerContent.includes('startup') || 
      lowerContent.includes('entrepreneur')) {
    return 'Small businesses and entrepreneurs';
  }
  
  // Healthcare indicators
  if (lowerContent.includes('health') || lowerContent.includes('medical') || 
      lowerContent.includes('patient') || lowerContent.includes('doctor')) {
    return 'Healthcare professionals and patients';
  }
  
  // Education indicators
  if (lowerContent.includes('student') || lowerContent.includes('education') || 
      lowerContent.includes('learning') || lowerContent.includes('school')) {
    return 'Students and educational institutions';
  }
  
  // Default fallback
  return 'General audience seeking quality solutions';
}

// Helper function to analyze brand voice and tone
function analyzeBrandVoice(content: string, title: string): string {
  const combined = `${title} ${content}`.toLowerCase();
  
  // Professional/Formal indicators
  if (combined.includes('enterprise') || combined.includes('solution') || 
      combined.includes('professional') || combined.includes('industry')) {
    return 'Professional and authoritative';
  }
  
  // Friendly/Casual indicators
  if (combined.includes('easy') || combined.includes('simple') || 
      combined.includes('friendly') || combined.includes('help')) {
    return 'Friendly and approachable';
  }
  
  // Energetic/Dynamic indicators
  if (combined.includes('fast') || combined.includes('quick') || 
      combined.includes('dynamic') || combined.includes('innovative')) {
    return 'Energetic and dynamic';
  }
  
  // Trustworthy/Reliable indicators
  if (combined.includes('secure') || combined.includes('reliable') || 
      combined.includes('trusted') || combined.includes('safe')) {
    return 'Trustworthy and reliable';
  }
  
  // Creative/Modern indicators
  if (combined.includes('creative') || combined.includes('modern') || 
      combined.includes('design') || combined.includes('innovative')) {
    return 'Creative and modern';
  }
  
  // Default fallback
  return 'Professional and engaging';
}

// Define the scene object structure
const SceneSchema = z.object({
  sceneNumber: z.number().describe('Sequential scene number'),
  duration: z.number().describe('Duration of scene in seconds'),
  visualType: z.enum(['image', 'text_overlay', 'video', 'mixed']).describe('Type of visual content'),
  visualDescription: z.string().describe('Detailed description of what should be shown visually'),
  hasVoiceover: z.boolean().describe('Whether this scene should have voiceover'),
  voiceoverText: z.string().nullable().describe('Text for voiceover if hasVoiceover is true'),
  hasSound: z.boolean().describe('Whether to generate video with background sound/music'),
  soundDescription: z.string().nullable().describe('Description of desired background sound/music'),
  textOverlays: z.array(z.object({
    text: z.string().describe('Text content for overlay'),
    position: z.enum(['top', 'center', 'bottom', 'top-left', 'top-right', 'bottom-left', 'bottom-right']).describe('Position of text overlay'),
    style: z.string().describe('Style description for text (font, color, size, etc.)')
  })).nullable().describe('Text overlays for this scene'),
  transition: z.string().nullable().describe('Transition effect to next scene'),
  keyMessage: z.string().describe('Main message or purpose of this scene'),
  notes: z.string().nullable().describe('Additional production notes')
});

// Create the generateScenes tool
const generateScenesTool = tool({
  name: 'generateScenes',
  description: 'Scrape a website and generate detailed scene-by-scene breakdown for a promotional video',
  parameters: z.object({
    url: z.string().describe('The website URL to scrape for content'),
    videoType: z.string().describe('Type of video being created (e.g., Promo Video)'),
    videoDuration: z.number().describe('Total video duration in seconds'),
    userPrompt: z.string().nullable().describe('Additional user requirements or style preferences'),
    aspectRatio: z.string().describe('Video aspect ratio')
  }),
  async execute({ url, videoType, videoDuration, userPrompt, aspectRatio }, runContext) {
    try {
      console.log('🎬 [GENERATION-AGENT] Starting scene generation process...');
      console.log('🎬 [GENERATION-AGENT] Parameters:', { url, videoType, videoDuration, userPrompt, aspectRatio });
      
      // Get context from the run context
      const context = runContext?.context as GenerationContext;
      if (!context) {
        throw new Error('Context not available');
      }

      // Step 1: Scrape the website for content
      console.log('🕷️ [GENERATION-AGENT] Scraping website for content:', url);
      const scrapedContent = await scrapeWebsite(url);
      console.log('🕷️ [GENERATION-AGENT] Scraped content:', {
        title: scrapedContent.title,
        contentLength: scrapedContent.content.length,
        url: scrapedContent.url
      });

      // Step 2: Analyze website content
      console.log('📊 [GENERATION-AGENT] Analyzing website content...');
      
      // Create comprehensive content analysis
      const contentAnalysis = {
        websiteUrl: url,
        contentSummary: `${scrapedContent.title} - ${scrapedContent.content.substring(0, 200)}...`,
        keyFeaturesBenefits: extractKeyFeatures(scrapedContent.content),
        targetAudience: analyzeTargetAudience(scrapedContent.content),
        toneBrandVoice: analyzeBrandVoice(scrapedContent.content, scrapedContent.title)
      };

      console.log('📊 [GENERATION-AGENT] Content Analysis:', {
        websiteUrl: contentAnalysis.websiteUrl,
        contentSummary: contentAnalysis.contentSummary.substring(0, 100) + '...',
        keyFeaturesCount: contentAnalysis.keyFeaturesBenefits.length,
        targetAudience: contentAnalysis.targetAudience,
        toneBrandVoice: contentAnalysis.toneBrandVoice
      });

      // Step 3: Generate scenes based on scraped content
      console.log('🎨 [GENERATION-AGENT] Generating scenes...');
      
      // Calculate average scene duration
      const avgSceneDuration = videoDuration <= 30 ? 3 : videoDuration <= 60 ? 5 : 8;
      const estimatedScenes = Math.ceil(videoDuration / avgSceneDuration);
      
      console.log('🎨 [GENERATION-AGENT] Estimated scenes:', estimatedScenes, 'with avg duration:', avgSceneDuration);

      // Create detailed scenes based on the scraped content
      const scenes = [];
      
      // Scene 1: Hook/Opening (always first)
        scenes.push({
          sceneNumber: 1,
          duration: Math.min(5, videoDuration * 0.2),
          visualType: 'mixed',
          visualDescription: `Dynamic opening scene featuring the main value proposition from "${scrapedContent.title}". Show engaging visuals that immediately capture attention - could be product shots, happy customers, or compelling statistics. Style should be energetic and professional.`,
          hasVoiceover: true,
          voiceoverText: `Hook viewers with the main benefit or problem solution from the website content`,
          hasSound: true,
          soundDescription: 'Upbeat, energetic background music that builds excitement',
          textOverlays: [{
            text: scrapedContent.title || 'Transform Your Business',
            position: 'center',
            style: 'Large, bold font with vibrant colors, fade-in animation'
          }],
          transition: 'Dynamic zoom or slide transition',
          keyMessage: 'Grab attention and establish value proposition',
          notes: 'This scene should immediately communicate why viewers should keep watching'
        });

      // Scene 2: Problem/Challenge
      if (estimatedScenes >= 3) {
        scenes.push({
          sceneNumber: 2,
          duration: avgSceneDuration,
          visualType: 'text_overlay',
          visualDescription: 'Clean, professional background with focus on text and simple graphics that illustrate the problem or challenge being addressed',
          hasVoiceover: true,
          voiceoverText: 'Explain the main problem or challenge that the website/product addresses',
          hasSound: true,
          soundDescription: 'Subtle, contemplative background music',
          textOverlays: [{
            text: 'The Challenge',
            position: 'top',
            style: 'Medium font, professional color scheme'
          }],
          transition: 'Smooth fade transition',
          keyMessage: 'Establish the problem that needs solving',
          notes: 'Build empathy with the audience by highlighting their pain points'
        });
      }

      // Middle scenes: Solution/Features (based on content)
      const middleScenes = Math.max(1, estimatedScenes - 3);
      for (let i = 0; i < middleScenes; i++) {
        const sceneNum = scenes.length + 1;
        scenes.push({
          sceneNumber: sceneNum,
          duration: avgSceneDuration,
          visualType: i % 2 === 0 ? 'video' : 'image',
          visualDescription: `Professional scene showcasing key features or benefits. ${i % 2 === 0 ? 'Show the product/service in action with real people using it' : 'High-quality images or graphics demonstrating specific features'}. Maintain consistent branding and visual style.`,
          hasVoiceover: true,
          voiceoverText: `Explain key benefit or feature #${i + 1} derived from website content`,
          hasSound: true,
          soundDescription: 'Consistent background music that matches the brand tone',
          textOverlays: [{
            text: `Key Benefit ${i + 1}`,
            position: i % 2 === 0 ? 'bottom' : 'top',
            style: 'Clean, readable font with brand colors'
          }],
          transition: 'Professional transition effect',
          keyMessage: `Highlight specific benefit or feature`,
          notes: `Focus on how this addresses the problem established earlier`
        });
      }

      // Final scene: Call to Action
      scenes.push({
        sceneNumber: scenes.length + 1,
        duration: Math.max(3, videoDuration * 0.2),
        visualType: 'mixed',
        visualDescription: 'Strong, compelling call-to-action scene with clear branding. Show contact information, website, or next steps. Include any relevant logos, phone numbers, or website URLs from the scraped content.',
        hasVoiceover: true,
        voiceoverText: 'Clear call-to-action encouraging viewers to take the next step',
        hasSound: true,
        soundDescription: 'Uplifting, motivational music that encourages action',
        textOverlays: [{
          text: 'Take Action Today',
          position: 'center',
          style: 'Bold, attention-grabbing font with contrasting colors'
        }, {
          text: url,
          position: 'bottom',
          style: 'Clean, readable font for contact information'
        }],
        transition: 'Fade to end',
        keyMessage: 'Drive viewers to take action',
        notes: 'Include clear next steps and contact information'
      });

      // Log the complete scene breakdown
      console.log('🎬 [GENERATION-AGENT] COMPLETE SCENE BREAKDOWN:');
      console.log('=' .repeat(80));
      console.log(`📝 Video Type: ${videoType}`);
      console.log(`⏱️  Total Duration: ${videoDuration} seconds`);
      console.log(`🎯 Aspect Ratio: ${aspectRatio}`);
      console.log(`🌐 Source URL: ${url}`);
      console.log(`📄 Website Title: ${scrapedContent.title}`);
      console.log(`💭 User Prompt: ${userPrompt || 'None provided'}`);
      console.log(`🎬 Total Scenes: ${scenes.length}`);
      console.log('=' .repeat(80));
      
      // Log website content analysis
      console.log('\n📊 [GENERATION-AGENT] WEBSITE CONTENT ANALYSIS:');
      console.log(`Website URL: ${contentAnalysis.websiteUrl}`);
      console.log(`Content Summary: ${contentAnalysis.contentSummary}`);
      console.log(`Key Features/Benefits:`);
      contentAnalysis.keyFeaturesBenefits.forEach((feature, i) => {
        console.log(`  ${i + 1}. ${feature}`);
      });
      console.log(`Target Audience: ${contentAnalysis.targetAudience}`);
      console.log(`Tone/Brand Voice: ${contentAnalysis.toneBrandVoice}`);
      console.log('=' .repeat(80));

      scenes.forEach((scene, index) => {
        console.log(`\n🎬 SCENE ${scene.sceneNumber}:`);
        console.log(`   Duration: ${scene.duration}s`);
        console.log(`   Visual Type: ${scene.visualType}`);
        console.log(`   Key Message: ${scene.keyMessage}`);
        console.log(`   Visual Description: ${scene.visualDescription}`);
        console.log(`   Has Voiceover: ${scene.hasVoiceover}`);
        if (scene.voiceoverText) {
          console.log(`   Voiceover: ${scene.voiceoverText}`);
        }
        console.log(`   Has Sound: ${scene.hasSound}`);
        if (scene.soundDescription) {
          console.log(`   Sound: ${scene.soundDescription}`);
        }
        if (scene.textOverlays && scene.textOverlays.length > 0) {
          console.log(`   Text Overlays:`);
          scene.textOverlays.forEach((overlay, i) => {
            console.log(`     ${i + 1}. "${overlay.text}" (${overlay.position}) - ${overlay.style}`);
          });
        }
        if (scene.transition) {
          console.log(`   Transition: ${scene.transition}`);
        }
        if (scene.notes) {
          console.log(`   Notes: ${scene.notes}`);
        }
        console.log('   ' + '-'.repeat(50));
      });

      console.log('\n🎬 [GENERATION-AGENT] Scene generation completed successfully!');

      return `🎬 **Video Scenes Generated Successfully!**

I've created a detailed ${scenes.length}-scene breakdown for your ${videoType} based on the content from ${url}.

**Video Overview:**
- **Total Duration:** ${videoDuration} seconds
- **Aspect Ratio:** ${aspectRatio}
- **Source Website:** ${scrapedContent.title}
- **Total Scenes:** ${scenes.length}

**Website Content Analysis:**
Website URL: ${contentAnalysis.websiteUrl}
Content Summary: ${contentAnalysis.contentSummary}
Key Features/Benefits: ${contentAnalysis.keyFeaturesBenefits.join(', ')}
Target Audience: ${contentAnalysis.targetAudience}
Tone/Brand Voice: ${contentAnalysis.toneBrandVoice}

**Scene Breakdown:**
${scenes.map(scene => `
**Scene ${scene.sceneNumber}** (${scene.duration}s)
- **Visual:** ${scene.visualDescription}
- **Key Message:** ${scene.keyMessage}
- **Voiceover:** ${scene.hasVoiceover ? 'Yes' : 'No'}
- **Background Sound:** ${scene.hasSound ? 'Yes' : 'No'}
${scene.textOverlays && scene.textOverlays.length > 0 ? `- **Text Overlays:** ${scene.textOverlays.map(t => `"${t.text}" (${t.position})`).join(', ')}` : ''}
`).join('\n')}

${userPrompt && userPrompt.trim() ? `\n**User Requirements Incorporated:** ${userPrompt}` : ''}

The detailed scene breakdown has been logged for the development team to use in the next step of video creation. Each scene includes specific visual descriptions, voiceover requirements, text overlays, and production notes based on your website content.`;

    } catch (error) {
      console.error('🚨 [GENERATION-AGENT] Error generating scenes:', error);
      return `I apologize, but I encountered an error while generating the video scenes: ${error instanceof Error ? error.message : 'Unknown error'}. Please check the URL and try again, or contact support if the issue persists.`;
    }
  },
});

export async function createGenerationAgent(context: GenerationContext) {
  const systemInstructions = `You are an AI assistant specialized in generating detailed video scene breakdowns for promotional videos.

Your primary function is to:
1. Take a website URL, video specifications, and optional user requirements
2. Scrape the website to understand the content and value proposition
3. Generate a detailed, scene-by-scene breakdown for a promotional video
4. Create professional video scripts with specific visual, audio, and overlay instructions

**Your Tool:**
- **generateScenes**: Scrapes a website and creates a comprehensive scene breakdown

**Your Workflow:**
1. Analyze the user's request for video type, duration, and source URL
2. Use the generateScenes tool with the provided parameters
3. The tool will automatically scrape the website and generate detailed scenes
4. Return a natural language summary of the generated scenes

**Scene Generation Guidelines:**
- Create scenes that tell a compelling story (Hook → Problem → Solution → CTA)
- Include specific visual descriptions for each scene
- Specify voiceover requirements and text overlays
- Consider pacing and transitions between scenes
- Incorporate user preferences when provided
- Ensure scenes fit within the specified total duration

**Context:**
- User ID: ${context.userId}
- Company ID: ${context.companyId}
- Conversation ID: ${context.conversationId}

**Previous conversation:**
${context.messages.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

Remember: Always use the generateScenes tool when the user provides video generation requirements. Don't try to create scenes manually - the tool handles website scraping and detailed scene generation automatically.`;

  const agent = new Agent({
    name: 'Video Scene Generation Assistant',
    instructions: systemInstructions,
    tools: [generateScenesTool],
    modelSettings: {
      temperature: 0.7,
      maxTokens: 4000,
    },
  });

  return agent;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export async function streamGenerationChat({
  messages,
  context,
}: {
  messages: ChatMessage[];
  context: GenerationContext;
}) {
  console.log('🎬 [GENERATION-AGENT] Creating generation agent...');
  
  const agent = await createGenerationAgent(context);
  const userMessage = messages[messages.length - 1]?.content || '';
  
  console.log('🎬 [GENERATION-AGENT] Agent created, user message:', userMessage);
  
  return {
    agent,
    userMessage,
  };
}
