import { Agent, tool } from '@openai/agents';
import { z } from 'zod';
import { repurposeVideo } from '../services/repurpose-video.js';
import dotenv from 'dotenv';
const postgres = await import('postgres');
dotenv.config();

interface RepurposeContext {
  userId: string;
  companyId: string;
  conversationId: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    created_at: number;
  }>;
}

// Define the video clip schema
const VideoClipSchema = z.object({
  clipNumber: z.number().describe('Sequential clip number'),
  title: z.string().describe('Descriptive title for the clip'),
  startTime: z.number().describe('Start time in seconds'),
  endTime: z.number().describe('End time in seconds'),
  duration: z.number().describe('Duration in seconds'),
  description: z.string().describe('Description of what happens in this clip'),
  keyMessage: z.string().describe('Main message or value of this clip'),
  reasoning: z.string().describe('Why this clip was selected for repurposing')
});

// Create the repurposeVideo tool
const repurposeVideoTool = tool({
  name: 'repurposeVideo',
  description: 'Process extracted video clips and create overlay structures for the frontend',
  parameters: z.object({
    clips: z.array(VideoClipSchema).describe('Array of video clips with start/end times extracted from transcript analysis'),
    assetId: z.string().describe('ID of the asset being repurposed'),
    assetPath: z.string().describe('File path of the asset'),
    fileName: z.string().describe('Name of the original file'),
    fileType: z.string().describe('MIME type of the file')
  }),
  async execute({ clips, assetId, assetPath, fileName, fileType }, runContext) {
    try {
      console.log('🎬 [REPURPOSE-VIDEO-TOOL] Processing extracted clips...');
      console.log('🎬 [REPURPOSE-VIDEO-TOOL] Parameters:', { 
        assetId, 
        fileName, 
        fileType,
        clipsCount: clips.length
      });
      
      // Log the received clips array (this is what the agent extracted from transcript)
      console.log('\n🎬 [REPURPOSE-VIDEO-TOOL] RECEIVED CLIPS FROM AGENT:');
      console.log('=' .repeat(80));
      console.log(`📁 File: ${fileName} (${fileType})`);
      console.log(`📊 Clips Received: ${clips.length}`);
      console.log('=' .repeat(80));

      clips.forEach((clip) => {
        console.log(`\n🎬 CLIP ${clip.clipNumber}: ${clip.title}`);
        console.log(`   ⏱️  Timestamp: ${clip.startTime}s → ${clip.endTime}s (${clip.duration}s)`);
        console.log(`   🎯 Key Message: ${clip.keyMessage}`);
        console.log(`   📝 Description: ${clip.description}`);
        console.log(`   💡 Reasoning: ${clip.reasoning}`);
        console.log('   ' + '-'.repeat(60));
      });

      console.log('\n🎬 [REPURPOSE-VIDEO-TOOL] Clips logged successfully!');
      
      // Now call the repurpose service to create overlays
      console.log('🎬 [REPURPOSE-VIDEO-TOOL] Creating video overlays...');
      const { repurposeVideo } = await import('../services/repurpose-video.js');
      
      // Get transcript data from run context
      const transcriptContent = runContext?.context?.processedTranscript || '';
      console.log(`🎬 [REPURPOSE-VIDEO-TOOL] Using transcript data: ${transcriptContent.length} characters`);
      console.log(`🎬 [REPURPOSE-VIDEO-TOOL] Run context keys:`, Object.keys(runContext || {}));
      console.log(`🎬 [REPURPOSE-VIDEO-TOOL] Context keys:`, Object.keys(runContext?.context || {}));
      
      const repurposeResult = await repurposeVideo({
        assetId,
        assetPath,
        userPrompt: 'Create clips from extracted segments',
        fileName,
        fileType,
        hasTranscript: true,
        transcriptContent // Pass the transcript content directly
      }, clips);
      
      // Insert overlays directly into the database (like video generation success handler)
      if (repurposeResult.overlays && repurposeResult.overlays.length > 0) {
        console.log(`🎬 [REPURPOSE-VIDEO-TOOL] Inserting ${repurposeResult.overlays.length} overlays directly into database...`);
        
        try {
          // Import postgres client (same as generate-video.ts)
          
          const sql = postgres.default(process.env.ZERO_UPSTREAM_DB! as string);
          
          // Get scene_id and project_id from run context
          const sceneId = runContext?.context?.sceneId || runContext?.context?.scene_id;
          const projectId = runContext?.context?.projectId || runContext?.context?.project_id;
          const userId = runContext?.context?.userId || runContext?.context?.user_id;
          
          console.log(`🎬 [REPURPOSE-VIDEO-TOOL] Database context:`, { sceneId, projectId, userId });
          
          if (!sceneId || !userId) {
            console.error(`❌ [REPURPOSE-VIDEO-TOOL] Missing required context: sceneId=${sceneId}, userId=${userId}`);
            throw new Error('Missing scene_id or user_id for overlay insertion');
          }
          
          // Insert each overlay into the database
          for (const overlay of repurposeResult.overlays) {
            // Create full URL for the video asset
            const videoUrl = `${process.env.SUPABASE_URL}/storage/v1/object/public/brand-assets/${overlay.src}`;
            
            const overlayRecord = {
              id: overlay.id,
              scene_id: sceneId,
              project_id: projectId || '', // For compatibility
              type: overlay.type,
              from: overlay.from,
              durationInFrames: overlay.durationInFrames,
              row: overlay.row,
              left: overlay.left,
              top: overlay.top,
              width: overlay.width,
              height: overlay.height,
              src: videoUrl, // Full URL for frontend to use
              content: videoUrl, // Also store in content for overlay reference
              isDragging: overlay.isDragging,
              rotation: overlay.rotation,
              loading: overlay.loading || false,
              styles: sql.json(overlay.styles), // Store as JSONB object
              videoStartTime: overlay.videoStartTime,
              duration: overlay.duration,
              created_at: Date.now(),
              updated_at: Date.now()
            };
            
            console.log(`🎬 [REPURPOSE-VIDEO-TOOL] Inserting overlay ${overlay.id} for clip "${overlay.metadata?.clipTitle || 'Unknown'}"`);
            console.log(`🎬 [REPURPOSE-VIDEO-TOOL] Video URL: ${videoUrl}`);
            
            await sql`
              INSERT INTO video_project_overlays (
                id, scene_id, project_id, type, "from", "durationInFrames", row, 
                "left", "top", width, height, src, content, "isDragging", rotation, 
                loading, styles, "videoStartTime", duration, created_at, updated_at
              ) VALUES (
                ${overlayRecord.id}, ${overlayRecord.scene_id}, ${overlayRecord.project_id}, 
                ${overlayRecord.type}, ${overlayRecord.from}, ${overlayRecord.durationInFrames}, 
                ${overlayRecord.row}, ${overlayRecord.left}, ${overlayRecord.top}, 
                ${overlayRecord.width}, ${overlayRecord.height}, ${overlayRecord.src}, 
                ${overlayRecord.content}, ${overlayRecord.isDragging}, ${overlayRecord.rotation}, 
                ${overlayRecord.loading}, ${overlayRecord.styles}, ${overlayRecord.videoStartTime}, 
                ${overlayRecord.duration}, ${overlayRecord.created_at}, ${overlayRecord.updated_at}
              )
              ON CONFLICT (id) DO UPDATE SET
                src = EXCLUDED.src,
                content = EXCLUDED.content,
                "videoStartTime" = EXCLUDED."videoStartTime",
                duration = EXCLUDED.duration,
                updated_at = EXCLUDED.updated_at
            `;
            
            console.log(`✅ [REPURPOSE-VIDEO-TOOL] Successfully inserted overlay ${overlay.id}`);
          }
          
          console.log(`🎉 [REPURPOSE-VIDEO-TOOL] All ${repurposeResult.overlays.length} overlays inserted into database successfully!`);
          
        } catch (dbError) {
          console.error(`❌ [REPURPOSE-VIDEO-TOOL] Error inserting overlays into database:`, dbError);
          // Don't throw - let the tool continue and return success message
        }
      }
      
      const totalDuration = clips.reduce((sum, clip) => sum + clip.duration, 0);
      
      return `🎬 **Video Repurpose Analysis Complete!**

I've successfully analyzed your ${fileType.includes('video') ? 'video' : 'audio'} file **"${fileName}"** and extracted ${clips.length} strategic clips based on your repurpose instructions.

## 📊 **Analysis Summary**
- **File:** ${fileName}
- **Type:** ${fileType.includes('video') ? '🎥 Video' : '🎵 Audio'}
- **Clips Generated:** ${clips.length}
- **Total Clips Duration:** ${Math.round(totalDuration)}s (${Math.round(totalDuration / 60)}m ${totalDuration % 60}s)

## ✂️ **Generated Clips**

${clips.map(clip => `### 🎬 Clip ${clip.clipNumber}: "${clip.title}"
⏱️ **Timing:** ${clip.startTime}s → ${clip.endTime}s (${clip.duration}s duration)  
🎯 **Key Message:** ${clip.keyMessage}  
📝 **Description:** ${clip.description}  
💡 **Why This Clip:** ${clip.reasoning}

---`).join('\n\n')}

## ✅ **Next Steps**
Your clips have been processed and are ready for timeline integration. Each clip has been strategically selected to maximize engagement and deliver value based on your specific repurpose requirements.

The clips are now logged in the system and ready for video editing implementation.`;

    } catch (error) {
      console.error('🚨 [REPURPOSE-VIDEO-TOOL] Error processing clips:', error);
      return `❌ Error processing clips: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  },
});

export async function createRepurposeAgent(context: RepurposeContext) {
  console.log('🎬 [REPURPOSE-AGENT] Creating repurpose agent...');
  console.log('🎬 [REPURPOSE-AGENT] Context:', context);
  const systemInstructions = `You are an AI assistant specialized in repurposing video and audio content for maximum impact.

Your primary function is to:
1. **Analyze the provided transcript content** to understand content structure and flow
2. **Identify key moments, highlights, and valuable segments** based on user instructions
3. **Extract strategic clips with precise start/end timestamps** 
4. **Use the repurposeVideo tool** to process the extracted clips

**Your Workflow:**
1. **Analyze the transcript content** that will be provided in the user message
2. **Based on user repurpose instructions**, identify the best segments to extract
3. **Create an array of clip objects** with precise timestamps (start/end times in seconds)
4. **Call the repurposeVideo tool** with the extracted clips array
5. **Return a natural language summary** of the analysis and clips

**Repurpose Strategies:**
- **Social Media Clips**: Short, engaging segments (15-60 seconds)
- **Highlights Reel**: Key moments and best parts  
- **Educational Segments**: Step-by-step or instructional portions
- **Teasers**: Compelling previews to drive engagement
- **Key Quotes**: Memorable statements and insights
- **Topic-Specific**: Segments focused on particular subjects

**Clip Extraction Guidelines:**
- Each clip should have a clear purpose and key message
- Timestamps should be precise (in seconds)
- Clips should be self-contained and make sense independently
- Consider pacing and natural speech breaks
- Prioritize high-impact moments based on user instructions

**CRITICAL: Video Overlay Frame Calculations**
The video editor uses 30 FPS (frames per second). When creating clips, remember:
- All timing must be converted to frames: startFrame = startTime * 30, durationInFrames = duration * 30
- Video overlays have these key properties:
  * from: starting frame number (0-based, where clip appears on timeline)
  * durationInFrames: clip length in frames
  * videoStartTime: where to start reading from the original video (in seconds)
  * duration: clip duration in seconds
  * row: timeline row (ALL CLIPS SHOULD USE ROW 0 - place clips sequentially on the same row, not separate rows)

**IMPORTANT: Sequential Clip Placement**
- ALL repurposed video clips must be placed on ROW 0 (same row)
- Clips should be sequential in time: clip 1 starts at frame 0, clip 2 starts when clip 1 ends, etc.
- Calculate each clip's 'from' position based on the cumulative duration of previous clips
- This ensures clips play one after another on the same timeline row

**Context:**
- User ID: ${context.userId}
- Company ID: ${context.companyId}
- Conversation ID: ${context.conversationId}

**Previous conversation:**
${context.messages.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

Remember: You will receive the full transcript content in the user message. Analyze it thoroughly and extract the best clips based on the repurpose instructions, then use the repurposeVideo tool with your extracted clips array.`;

  const agent = new Agent({
    name: 'Video Repurpose Assistant',
    instructions: systemInstructions,
    tools: [repurposeVideoTool],
    modelSettings: {
      temperature: 0.7,
      maxTokens: 3000,
    },
  });

  return agent;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export async function streamRepurposeChat({
  messages,
  context,
}: {
  messages: ChatMessage[];
  context: RepurposeContext;
}) {
  console.log('🎬 [REPURPOSE-AGENT] Creating repurpose agent...');
  
  const agent = await createRepurposeAgent(context);
  const userMessage = messages[messages.length - 1]?.content || '';
  
  console.log('🎬 [REPURPOSE-AGENT] Agent created, user message:', userMessage);
  
  return {
    agent,
    userMessage,
  };
}
